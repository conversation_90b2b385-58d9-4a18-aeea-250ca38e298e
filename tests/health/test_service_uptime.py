"""
💩🎉TurdParty🎉💩 Service Uptime Test Suite

This test suite uses the centralized URL management system to check
service uptime before running other tests in parallel. It ensures all
required services are healthy and accessible.
"""

import asyncio
import pytest
import aiohttp
import time
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import sys

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from utils.service_urls import get_service_url_manager, ServiceURLManager


@dataclass
class ServiceHealthResult:
    """Result of a service health check"""
    service_name: str
    url: str
    status: str  # 'healthy', 'unhealthy', 'timeout', 'error'
    response_time: float
    status_code: Optional[int] = None
    error_message: Optional[str] = None
    details: Optional[Dict] = None


class ServiceUptimeChecker:
    """
    Service uptime checker using centralized URL management
    """
    
    def __init__(self, timeout: int = 10, max_retries: int = 3):
        """
        Initialize the uptime checker

        Args:
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries per service
        """
        self.url_manager = get_service_url_manager()
        self.timeout = timeout
        self.max_retries = max_retries

        # Define critical services that must be healthy
        self.critical_services = [
            'api',
            'status'
        ]

        # Define optional services (warnings if unhealthy)
        self.optional_services = [
            'frontend',
            'docs',
            'minio',
            'elasticsearch',
            'kibana',
            'vm-monitor'
        ]

        # Fallback URLs for direct port access when Traefik is not available
        self.fallback_urls = {
            'api': 'http://localhost:8000/health',
            'status': 'http://localhost:8090/',
            'frontend': 'http://localhost:3000/',
            'minio': 'http://localhost:9000/minio/health/live',
            'elasticsearch': 'http://localhost:9200/_cluster/health',
            'kibana': 'http://localhost:5601/api/status',
            'docs': 'http://localhost:8081/',
            'vm-monitor': 'http://localhost:8082/health'
        }
    
    async def check_service_health(self, service_name: str, session: aiohttp.ClientSession) -> ServiceHealthResult:
        """
        Check health of a single service

        Args:
            service_name: Name of the service to check
            session: aiohttp session for making requests

        Returns:
            ServiceHealthResult with health status
        """
        urls_to_try = []

        try:
            # Try Traefik URL first
            traefik_url = self.url_manager.get_service_url(service_name, include_health=True)
            urls_to_try.append(('traefik', traefik_url))
        except Exception:
            pass

        # Add fallback URL if available
        if service_name in self.fallback_urls:
            urls_to_try.append(('fallback', self.fallback_urls[service_name]))

        if not urls_to_try:
            return ServiceHealthResult(
                service_name=service_name,
                url='unknown',
                status='error',
                response_time=0,
                error_message="No URLs configured for service"
            )

        # Try each URL
        for url_type, service_url in urls_to_try:
            start_time = time.time()

            # Make health check request with retries
            for attempt in range(self.max_retries):
                try:
                    async with session.get(
                        service_url,
                        timeout=aiohttp.ClientTimeout(total=self.timeout)
                    ) as response:
                        response_time = time.time() - start_time

                        # Try to parse JSON response for details
                        details = None
                        try:
                            if response.content_type == 'application/json':
                                details = await response.json()
                        except Exception:
                            pass

                        # Determine health status based on response
                        if response.status == 200:
                            return ServiceHealthResult(
                                service_name=service_name,
                                url=f"{service_url} ({url_type})",
                                status='healthy',
                                response_time=response_time,
                                status_code=response.status,
                                details=details
                            )
                        elif response.status < 500:
                            # Client error but service is responding
                            return ServiceHealthResult(
                                service_name=service_name,
                                url=f"{service_url} ({url_type})",
                                status='unhealthy',
                                response_time=response_time,
                                status_code=response.status,
                                error_message=f"HTTP {response.status}",
                                details=details
                            )
                        # For 5xx errors, continue to next attempt/URL

                except asyncio.TimeoutError:
                    if attempt == self.max_retries - 1:
                        # Try next URL if available
                        break
                    # Wait before retry
                    await asyncio.sleep(1)

                except Exception as e:
                    if attempt == self.max_retries - 1:
                        # Try next URL if available
                        break
                    # Wait before retry
                    await asyncio.sleep(1)

        # All URLs failed
        return ServiceHealthResult(
            service_name=service_name,
            url=f"tried {len(urls_to_try)} URLs",
            status='error',
            response_time=time.time() - start_time if 'start_time' in locals() else 0,
            error_message="All URLs failed or timed out"
        )
    
    async def check_all_services(self) -> Dict[str, ServiceHealthResult]:
        """
        Check health of all services in parallel
        
        Returns:
            Dictionary mapping service names to health results
        """
        all_services = self.critical_services + self.optional_services
        
        async with aiohttp.ClientSession() as session:
            # Create tasks for all service health checks
            tasks = [
                self.check_service_health(service, session)
                for service in all_services
            ]
            
            # Run all health checks in parallel
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            health_results = {}
            for result in results:
                if isinstance(result, ServiceHealthResult):
                    health_results[result.service_name] = result
                else:
                    # Handle exceptions
                    health_results['unknown'] = ServiceHealthResult(
                        service_name='unknown',
                        url='unknown',
                        status='error',
                        response_time=0,
                        error_message=str(result)
                    )
            
            return health_results
    
    def analyze_health_results(self, results: Dict[str, ServiceHealthResult]) -> Tuple[bool, List[str], List[str]]:
        """
        Analyze health check results
        
        Args:
            results: Dictionary of service health results
            
        Returns:
            Tuple of (all_critical_healthy, critical_failures, warnings)
        """
        critical_failures = []
        warnings = []
        
        # Check critical services
        for service in self.critical_services:
            if service in results:
                result = results[service]
                if result.status != 'healthy':
                    critical_failures.append(
                        f"{service}: {result.status} - {result.error_message or 'Unknown error'}"
                    )
        
        # Check optional services
        for service in self.optional_services:
            if service in results:
                result = results[service]
                if result.status != 'healthy':
                    warnings.append(
                        f"{service}: {result.status} - {result.error_message or 'Unknown error'}"
                    )
        
        all_critical_healthy = len(critical_failures) == 0
        
        return all_critical_healthy, critical_failures, warnings


# Global uptime checker instance
uptime_checker = ServiceUptimeChecker()


@pytest.mark.asyncio
@pytest.mark.health
async def test_all_services_uptime():
    """Test that all services are accessible and healthy"""
    print("\n🔍 Checking service uptime using centralized URL management...")
    
    # Check all services
    results = await uptime_checker.check_all_services()
    
    # Analyze results
    all_critical_healthy, critical_failures, warnings = uptime_checker.analyze_health_results(results)
    
    # Print detailed results
    print(f"\n📊 Service Health Summary:")
    print(f"Environment: {uptime_checker.url_manager.get_current_environment()}")
    print(f"Total services checked: {len(results)}")
    
    # Print individual service results
    for service_name, result in results.items():
        status_emoji = {
            'healthy': '✅',
            'unhealthy': '❌',
            'timeout': '⏰',
            'error': '💥'
        }.get(result.status, '❓')
        
        print(f"  {status_emoji} {service_name}: {result.status} ({result.response_time:.2f}s)")
        if result.error_message:
            print(f"    Error: {result.error_message}")
    
    # Print warnings
    if warnings:
        print(f"\n⚠️ Warnings ({len(warnings)} optional services):")
        for warning in warnings:
            print(f"  - {warning}")
    
    # Print critical failures
    if critical_failures:
        print(f"\n❌ Critical Failures ({len(critical_failures)} required services):")
        for failure in critical_failures:
            print(f"  - {failure}")
    
    # Assert that all critical services are healthy
    assert all_critical_healthy, f"Critical services are not healthy: {critical_failures}"
    
    print(f"\n✅ All critical services are healthy!")


@pytest.mark.asyncio
@pytest.mark.health
async def test_api_endpoints_accessible():
    """Test that key API endpoints are accessible"""
    print("\n🔗 Testing API endpoint accessibility...")
    
    # Test key API endpoints
    test_endpoints = [
        ('health.system', {}),
        ('health.database', {}),
        ('health.elasticsearch', {}),
        ('files.list', {}),
    ]
    
    async with aiohttp.ClientSession() as session:
        for endpoint_name, params in test_endpoints:
            try:
                endpoint_url = uptime_checker.url_manager.get_api_endpoint(endpoint_name, params)
                
                async with session.get(
                    endpoint_url,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    status_emoji = '✅' if response.status < 400 else '❌'
                    print(f"  {status_emoji} {endpoint_name}: HTTP {response.status}")
                    
                    # Don't fail on 404s for list endpoints (might be empty)
                    if endpoint_name == 'files.list' and response.status == 404:
                        continue
                    
                    assert response.status < 500, f"Server error on {endpoint_name}: {response.status}"
            
            except Exception as e:
                print(f"  ❌ {endpoint_name}: Error - {e}")
                pytest.fail(f"Failed to access {endpoint_name}: {e}")
    
    print("✅ API endpoints are accessible!")


@pytest.mark.asyncio
@pytest.mark.health
async def test_service_response_times():
    """Test that services respond within acceptable time limits"""
    print("\n⏱️ Testing service response times...")
    
    # Check all services
    results = await uptime_checker.check_all_services()
    
    # Define acceptable response time thresholds
    time_thresholds = {
        'api': 2.0,        # API should respond quickly
        'status': 3.0,     # Status dashboard
        'minio': 2.0,      # Storage should be fast
        'elasticsearch': 5.0,  # Search can be slower
        'frontend': 5.0,   # Frontend build time
        'docs': 5.0,       # Documentation
        'kibana': 10.0,    # Analytics can be slower
        'vm-monitor': 3.0  # VM monitoring
    }
    
    slow_services = []
    
    for service_name, result in results.items():
        if result.status == 'healthy':
            threshold = time_thresholds.get(service_name, 5.0)
            
            if result.response_time > threshold:
                slow_services.append(f"{service_name}: {result.response_time:.2f}s (threshold: {threshold}s)")
                print(f"  ⚠️ {service_name}: {result.response_time:.2f}s (slow)")
            else:
                print(f"  ✅ {service_name}: {result.response_time:.2f}s")
    
    # Warn about slow services but don't fail
    if slow_services:
        print(f"\n⚠️ Slow services detected:")
        for slow_service in slow_services:
            print(f"  - {slow_service}")
    
    print("✅ Response time check completed!")


# Pytest fixtures for other tests to use
@pytest.fixture(scope="session")
async def healthy_services():
    """
    Session-scoped fixture that ensures all critical services are healthy
    before running other tests
    """
    print("\n🏥 Pre-test health check...")

    results = await uptime_checker.check_all_services()
    all_critical_healthy, critical_failures, warnings = uptime_checker.analyze_health_results(results)

    if not all_critical_healthy:
        pytest.skip(f"Critical services are not healthy: {critical_failures}")

    return results


@pytest.fixture
def service_urls():
    """Fixture providing service URLs for tests"""
    return uptime_checker.url_manager


@pytest.mark.asyncio
@pytest.mark.health
@pytest.mark.performance
async def test_service_performance_baseline():
    """Test service performance baselines for regression detection"""
    print("\n⚡ Testing service performance baselines...")

    # Performance thresholds (in seconds)
    performance_thresholds = {
        'api': {'p50': 0.5, 'p95': 2.0, 'p99': 5.0},
        'status': {'p50': 1.0, 'p95': 3.0, 'p99': 8.0},
        'minio': {'p50': 0.3, 'p95': 1.0, 'p99': 3.0},
        'elasticsearch': {'p50': 1.0, 'p95': 5.0, 'p99': 10.0}
    }

    # Run multiple requests to get performance distribution
    num_requests = 10
    performance_results = {}

    for service_name, thresholds in performance_thresholds.items():
        print(f"\n  Testing {service_name} performance...")

        response_times = []

        async with aiohttp.ClientSession() as session:
            for i in range(num_requests):
                try:
                    service_url = uptime_checker.url_manager.get_service_url(service_name, include_health=True)

                    start_time = time.time()
                    async with session.get(service_url, timeout=aiohttp.ClientTimeout(total=15)) as response:
                        response_time = time.time() - start_time

                        if response.status == 200:
                            response_times.append(response_time)

                except Exception as e:
                    print(f"    Request {i+1} failed: {e}")

        if response_times:
            # Calculate percentiles
            response_times.sort()
            p50 = response_times[len(response_times) // 2]
            p95 = response_times[int(len(response_times) * 0.95)]
            p99 = response_times[int(len(response_times) * 0.99)]

            performance_results[service_name] = {
                'p50': p50,
                'p95': p95,
                'p99': p99,
                'samples': len(response_times)
            }

            # Check against thresholds
            p50_ok = p50 <= thresholds['p50']
            p95_ok = p95 <= thresholds['p95']
            p99_ok = p99 <= thresholds['p99']

            print(f"    P50: {p50:.3f}s {'✅' if p50_ok else '❌'} (threshold: {thresholds['p50']}s)")
            print(f"    P95: {p95:.3f}s {'✅' if p95_ok else '❌'} (threshold: {thresholds['p95']}s)")
            print(f"    P99: {p99:.3f}s {'✅' if p99_ok else '❌'} (threshold: {thresholds['p99']}s)")

            # Only fail on P95 threshold violations (P99 can be more variable)
            if not p95_ok:
                pytest.fail(f"{service_name} P95 response time {p95:.3f}s exceeds threshold {thresholds['p95']}s")

        else:
            pytest.fail(f"No successful requests to {service_name}")

    print("\n✅ Performance baseline tests completed")


@pytest.mark.asyncio
@pytest.mark.health
@pytest.mark.load
async def test_concurrent_service_load():
    """Test services under concurrent load"""
    print("\n🔄 Testing concurrent service load...")

    # Test concurrent requests to API
    concurrent_requests = 20
    api_url = uptime_checker.url_manager.get_service_url('api', include_health=True)

    async def make_request(session, request_id):
        try:
            start_time = time.time()
            async with session.get(api_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                response_time = time.time() - start_time
                return {
                    'request_id': request_id,
                    'success': response.status == 200,
                    'response_time': response_time,
                    'status_code': response.status
                }
        except Exception as e:
            return {
                'request_id': request_id,
                'success': False,
                'response_time': 0,
                'error': str(e)
            }

    # Run concurrent requests
    async with aiohttp.ClientSession() as session:
        tasks = [make_request(session, i) for i in range(concurrent_requests)]
        results = await asyncio.gather(*tasks)

    # Analyze results
    successful_requests = [r for r in results if r['success']]
    failed_requests = [r for r in results if not r['success']]

    success_rate = len(successful_requests) / len(results) * 100

    if successful_requests:
        avg_response_time = sum(r['response_time'] for r in successful_requests) / len(successful_requests)
        max_response_time = max(r['response_time'] for r in successful_requests)
    else:
        avg_response_time = 0
        max_response_time = 0

    print(f"  Concurrent requests: {concurrent_requests}")
    print(f"  Success rate: {success_rate:.1f}%")
    print(f"  Average response time: {avg_response_time:.3f}s")
    print(f"  Max response time: {max_response_time:.3f}s")
    print(f"  Failed requests: {len(failed_requests)}")

    # Assert acceptable performance under load
    assert success_rate >= 90, f"Success rate {success_rate:.1f}% is below 90%"
    assert avg_response_time <= 3.0, f"Average response time {avg_response_time:.3f}s exceeds 3.0s"

    print("✅ Concurrent load test passed")

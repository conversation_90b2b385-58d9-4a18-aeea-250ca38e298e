"""
💩🎉TurdParty🎉💩 Service Dependency Tests

This test suite verifies that services can communicate with their dependencies
using the centralized URL management system. It tests the complete service
dependency chain before running other tests.
"""

import pytest
import asyncio
import aiohttp
import json
from typing import Dict, List, Tuple
from pathlib import Path
import sys

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from utils.service_urls import get_service_url_manager


class ServiceDependencyTester:
    """
    Test service dependencies and inter-service communication
    """
    
    def __init__(self):
        self.url_manager = get_service_url_manager()

        # Fallback URLs for direct port access
        self.fallback_urls = {
            'api': 'http://localhost:8000',
            'status': 'http://localhost:8090',
            'frontend': 'http://localhost:3000',
            'minio': 'http://localhost:9000',
            'elasticsearch': 'http://localhost:9200',
        }

        # Define service dependency chains
        self.dependency_chains = {
            'api': {
                'dependencies': ['minio', 'elasticsearch'],
                'endpoints': [
                    'health.database',
                    'health.elasticsearch',
                    'health.minio'
                ]
            },
            'frontend': {
                'dependencies': ['api'],
                'endpoints': []
            },
            'status': {
                'dependencies': ['api'],
                'endpoints': []
            }
        }

    def get_service_url_with_fallback(self, service_name: str, include_health: bool = False) -> str:
        """Get service URL with fallback to localhost ports"""
        try:
            return self.url_manager.get_service_url(service_name, include_health)
        except Exception:
            if service_name in self.fallback_urls:
                url = self.fallback_urls[service_name]
                if include_health:
                    if service_name == 'api':
                        url += '/health'
                    elif service_name == 'minio':
                        url += '/minio/health/live'
                    elif service_name == 'elasticsearch':
                        url += '/_cluster/health'
                return url
            raise

    def get_api_endpoint_with_fallback(self, endpoint: str, params: dict = None) -> str:
        """Get API endpoint with fallback to localhost"""
        try:
            return self.url_manager.get_api_endpoint(endpoint, params or {})
        except Exception:
            # Fallback to localhost API
            base_url = 'http://localhost:8000'
            endpoint_paths = {
                'health.database': '/api/v1/health/database',
                'health.elasticsearch': '/api/v1/health/elasticsearch',
                'health.minio': '/api/v1/health/minio',
                'health.system': '/health',
                'files.list': '/api/v1/files'
            }
            return base_url + endpoint_paths.get(endpoint, f'/api/v1/{endpoint}')
    
    async def test_database_connectivity(self) -> bool:
        """Test database connectivity through API"""
        try:
            endpoint_url = self.get_api_endpoint_with_fallback('health.database')

            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('status') == 'healthy'
            return False
        except Exception as e:
            print(f"Database connectivity test failed: {e}")
            return False
    
    async def test_elasticsearch_connectivity(self) -> bool:
        """Test Elasticsearch connectivity through API"""
        try:
            endpoint_url = self.get_api_endpoint_with_fallback('health.elasticsearch')

            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('status') == 'healthy'
            return False
        except Exception as e:
            print(f"Elasticsearch connectivity test failed: {e}")
            return False

    async def test_minio_connectivity(self) -> bool:
        """Test MinIO connectivity through API"""
        try:
            endpoint_url = self.get_api_endpoint_with_fallback('health.minio')

            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get('status') == 'healthy'
            return False
        except Exception as e:
            print(f"MinIO connectivity test failed: {e}")
            return False
    
    async def test_api_endpoints_functional(self) -> Dict[str, bool]:
        """Test that key API endpoints are functional"""
        endpoints_to_test = [
            ('health.system', {}),
            ('files.list', {}),
        ]
        
        results = {}
        
        async with aiohttp.ClientSession() as session:
            for endpoint_name, params in endpoints_to_test:
                try:
                    endpoint_url = self.url_manager.get_api_endpoint(endpoint_name, params)
                    
                    async with session.get(endpoint_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        # Accept 200, 404 (empty lists), but not 500+ errors
                        results[endpoint_name] = response.status < 500
                        
                except Exception as e:
                    print(f"API endpoint {endpoint_name} test failed: {e}")
                    results[endpoint_name] = False
        
        return results
    
    async def test_service_chain_connectivity(self, service_name: str) -> Tuple[bool, List[str]]:
        """
        Test complete service dependency chain
        
        Args:
            service_name: Name of the service to test
            
        Returns:
            Tuple of (success, error_messages)
        """
        if service_name not in self.dependency_chains:
            return True, []  # No dependencies defined
        
        chain = self.dependency_chains[service_name]
        errors = []
        
        # Test service itself
        try:
            service_url = self.url_manager.get_service_url(service_name, include_health=True)
            
            async with aiohttp.ClientSession() as session:
                async with session.get(service_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status >= 500:
                        errors.append(f"{service_name} service returned {response.status}")
        except Exception as e:
            errors.append(f"{service_name} service unreachable: {e}")
        
        # Test dependencies
        for dependency in chain['dependencies']:
            try:
                dep_url = self.url_manager.get_service_url(dependency, include_health=True)
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(dep_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        if response.status >= 500:
                            errors.append(f"Dependency {dependency} returned {response.status}")
            except Exception as e:
                errors.append(f"Dependency {dependency} unreachable: {e}")
        
        # Test specific endpoints
        for endpoint in chain['endpoints']:
            try:
                endpoint_url = self.url_manager.get_api_endpoint(endpoint)
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(endpoint_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        if response.status >= 500:
                            errors.append(f"Endpoint {endpoint} returned {response.status}")
            except Exception as e:
                errors.append(f"Endpoint {endpoint} unreachable: {e}")
        
        return len(errors) == 0, errors


# Global dependency tester
dependency_tester = ServiceDependencyTester()


@pytest.mark.asyncio
@pytest.mark.health
@pytest.mark.dependency
async def test_database_dependency():
    """Test database connectivity and health"""
    print("\n🗄️ Testing database dependency...")
    
    is_healthy = await dependency_tester.test_database_connectivity()
    
    assert is_healthy, "Database is not healthy or accessible through API"
    print("✅ Database dependency is healthy")


@pytest.mark.asyncio
@pytest.mark.health
@pytest.mark.dependency
async def test_elasticsearch_dependency():
    """Test Elasticsearch connectivity and health"""
    print("\n🔍 Testing Elasticsearch dependency...")
    
    is_healthy = await dependency_tester.test_elasticsearch_connectivity()
    
    assert is_healthy, "Elasticsearch is not healthy or accessible through API"
    print("✅ Elasticsearch dependency is healthy")


@pytest.mark.asyncio
@pytest.mark.health
@pytest.mark.dependency
async def test_minio_dependency():
    """Test MinIO connectivity and health"""
    print("\n📦 Testing MinIO dependency...")
    
    is_healthy = await dependency_tester.test_minio_connectivity()
    
    assert is_healthy, "MinIO is not healthy or accessible through API"
    print("✅ MinIO dependency is healthy")


@pytest.mark.asyncio
@pytest.mark.health
@pytest.mark.dependency
async def test_api_functionality():
    """Test API endpoint functionality"""
    print("\n🔗 Testing API endpoint functionality...")
    
    results = await dependency_tester.test_api_endpoints_functional()
    
    failed_endpoints = [endpoint for endpoint, success in results.items() if not success]
    
    if failed_endpoints:
        print(f"❌ Failed endpoints: {failed_endpoints}")
    
    for endpoint, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {endpoint}")
    
    assert len(failed_endpoints) == 0, f"API endpoints failed: {failed_endpoints}"
    print("✅ All API endpoints are functional")


@pytest.mark.asyncio
@pytest.mark.health
@pytest.mark.dependency
async def test_service_dependency_chains():
    """Test complete service dependency chains"""
    print("\n🔗 Testing service dependency chains...")
    
    all_errors = []
    
    for service_name in dependency_tester.dependency_chains.keys():
        print(f"\n  Testing {service_name} dependency chain...")
        
        success, errors = await dependency_tester.test_service_chain_connectivity(service_name)
        
        if success:
            print(f"    ✅ {service_name} chain is healthy")
        else:
            print(f"    ❌ {service_name} chain has issues:")
            for error in errors:
                print(f"      - {error}")
            all_errors.extend([f"{service_name}: {error}" for error in errors])
    
    assert len(all_errors) == 0, f"Service dependency chain failures: {all_errors}"
    print("\n✅ All service dependency chains are healthy")


@pytest.mark.asyncio
@pytest.mark.health
@pytest.mark.dependency
async def test_url_manager_configuration():
    """Test that URL manager is properly configured"""
    print("\n⚙️ Testing URL manager configuration...")
    
    # Test environment detection
    environment = dependency_tester.url_manager.get_current_environment()
    print(f"  Environment: {environment}")
    assert environment in ['development', 'staging', 'production', 'local']
    
    # Test service URL generation
    try:
        api_url = dependency_tester.url_manager.get_service_url('api')
        print(f"  API URL: {api_url}")
        assert api_url.startswith(('http://', 'https://'))
    except Exception as e:
        pytest.fail(f"Failed to generate API URL: {e}")
    
    # Test API endpoint generation
    try:
        health_endpoint = dependency_tester.url_manager.get_api_endpoint('health.system')
        print(f"  Health endpoint: {health_endpoint}")
        assert '/health' in health_endpoint
    except Exception as e:
        pytest.fail(f"Failed to generate health endpoint: {e}")
    
    # Test bucket configuration
    try:
        uploads_bucket = dependency_tester.url_manager.get_minio_bucket('uploads')
        print(f"  Uploads bucket: {uploads_bucket}")
        assert uploads_bucket == 'turdparty-uploads'
    except Exception as e:
        pytest.fail(f"Failed to get MinIO bucket: {e}")
    
    print("✅ URL manager configuration is valid")


@pytest.mark.asyncio
@pytest.mark.health
@pytest.mark.dependency
async def test_cross_service_communication():
    """Test communication between services"""
    print("\n🌐 Testing cross-service communication...")
    
    # Test that status dashboard can reach API
    try:
        status_url = dependency_tester.url_manager.get_service_url('status')
        api_health_url = dependency_tester.url_manager.get_api_endpoint('health.system')
        
        async with aiohttp.ClientSession() as session:
            # Test status service
            async with session.get(status_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                status_accessible = response.status < 500
            
            # Test API health from status perspective (relative URL)
            relative_health = dependency_tester.url_manager.get_relative_api_endpoint('health.system')
            
            print(f"  Status service accessible: {'✅' if status_accessible else '❌'}")
            print(f"  API health endpoint: {api_health_url}")
            print(f"  Relative health endpoint: {relative_health}")
            
            assert status_accessible, "Status service is not accessible"
            assert relative_health == '/health', "Relative health endpoint is incorrect"
    
    except Exception as e:
        pytest.fail(f"Cross-service communication test failed: {e}")
    
    print("✅ Cross-service communication is working")

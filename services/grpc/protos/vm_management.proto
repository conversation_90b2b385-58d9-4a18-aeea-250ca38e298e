syntax = "proto3";

package turdparty.vm;

// VM Management Service for TurdParty
service VMManagement {
    // VM Lifecycle Operations
    rpc GetVMStatus(VMStatusRequest) returns (VMStatusResponse);
    rpc StartVM(StartVMRequest) returns (StartVMResponse);
    rpc StopVM(StopVMRequest) returns (StopVMResponse);
    rpc RestartVM(RestartVMRequest) returns (RestartVMResponse);
    
    // File Operations
    rpc TransferFile(stream FileChunk) returns (TransferResponse);
    rpc ExecuteCommand(CommandRequest) returns (stream CommandResponse);
    rpc ListFiles(ListFilesRequest) returns (ListFilesResponse);
    
    // Monitoring and Metrics
    rpc GetMetrics(MetricsRequest) returns (MetricsResponse);
    rpc StreamMetrics(MetricsRequest) returns (stream MetricsResponse);
    rpc GetProcessList(ProcessListRequest) returns (ProcessListResponse);
    
    // Windows-specific Operations
    rpc ExecuteWindowsCommand(WindowsCommandRequest) returns (stream CommandResponse);
    rpc GetWindowsServices(WindowsServicesRequest) returns (WindowsServicesResponse);
    rpc GetRegistryValue(RegistryRequest) returns (RegistryResponse);
}

// VM Status Operations
message VMStatusRequest {
    string vm_id = 1;
}

message VMStatusResponse {
    string vm_id = 1;
    string status = 2;  // creating, running, stopped, error
    string ip_address = 3;
    int32 ssh_port = 4;
    int32 winrm_port = 5;
    double cpu_usage = 6;
    double memory_usage = 7;
    int64 uptime_seconds = 8;
    string error_message = 9;
}

// VM Control Operations
message StartVMRequest {
    string vm_id = 1;
    bool force = 2;
}

message StartVMResponse {
    bool success = 1;
    string message = 2;
    string error = 3;
}

message StopVMRequest {
    string vm_id = 1;
    bool force = 2;
}

message StopVMResponse {
    bool success = 1;
    string message = 2;
    string error = 3;
}

message RestartVMRequest {
    string vm_id = 1;
    bool force = 2;
}

message RestartVMResponse {
    bool success = 1;
    string message = 2;
    string error = 3;
}

// File Transfer Operations
message FileChunk {
    string vm_id = 1;
    string file_path = 2;
    bytes data = 3;
    int64 offset = 4;
    int64 total_size = 5;
    bool is_last_chunk = 6;
    string blake3_hash = 7;  // For integrity verification
}

message TransferResponse {
    bool success = 1;
    string message = 2;
    string error = 3;
    int64 bytes_transferred = 4;
    string blake3_hash = 5;
}

// Command Execution
message CommandRequest {
    string vm_id = 1;
    string command = 2;
    repeated string args = 3;
    string working_directory = 4;
    map<string, string> environment = 5;
    int32 timeout_seconds = 6;
}

message CommandResponse {
    string vm_id = 1;
    string stdout = 2;
    string stderr = 3;
    int32 exit_code = 4;
    bool is_complete = 5;
    string error = 6;
}

// Windows-specific Commands
message WindowsCommandRequest {
    string vm_id = 1;
    string command = 2;
    repeated string args = 3;
    string working_directory = 4;
    bool run_as_admin = 5;
    int32 timeout_seconds = 6;
}

// File Listing
message ListFilesRequest {
    string vm_id = 1;
    string path = 2;
    bool recursive = 3;
}

message ListFilesResponse {
    repeated FileInfo files = 1;
    string error = 2;
}

message FileInfo {
    string name = 1;
    string path = 2;
    int64 size = 3;
    string modified_time = 4;
    bool is_directory = 5;
    string permissions = 6;
}

// Metrics and Monitoring
message MetricsRequest {
    string vm_id = 1;
    bool include_processes = 2;
    bool include_network = 3;
    bool include_disk = 4;
}

message MetricsResponse {
    string vm_id = 1;
    string timestamp = 2;
    double cpu_percent = 3;
    double memory_percent = 4;
    int64 memory_used_bytes = 5;
    int64 memory_total_bytes = 6;
    double disk_usage_percent = 7;
    int64 disk_used_bytes = 8;
    int64 disk_total_bytes = 9;
    repeated ProcessInfo processes = 10;
    NetworkStats network = 11;
}

message ProcessInfo {
    int32 pid = 1;
    string name = 2;
    string command_line = 3;
    double cpu_percent = 4;
    int64 memory_bytes = 5;
    string status = 6;
    string user = 7;
}

message NetworkStats {
    int64 bytes_sent = 1;
    int64 bytes_received = 2;
    int64 packets_sent = 3;
    int64 packets_received = 4;
    repeated NetworkConnection connections = 5;
}

message NetworkConnection {
    string local_address = 1;
    int32 local_port = 2;
    string remote_address = 3;
    int32 remote_port = 4;
    string status = 5;
    int32 pid = 6;
}

// Process List
message ProcessListRequest {
    string vm_id = 1;
    bool include_details = 2;
}

message ProcessListResponse {
    repeated ProcessInfo processes = 1;
    string error = 2;
}

// Windows Services
message WindowsServicesRequest {
    string vm_id = 1;
}

message WindowsServicesResponse {
    repeated WindowsService services = 1;
    string error = 2;
}

message WindowsService {
    string name = 1;
    string display_name = 2;
    string status = 3;
    string start_type = 4;
    string path = 5;
}

// Windows Registry
message RegistryRequest {
    string vm_id = 1;
    string key_path = 2;
    string value_name = 3;
}

message RegistryResponse {
    string value = 1;
    string type = 2;
    string error = 3;
}

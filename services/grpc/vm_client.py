#!/usr/bin/env python3
"""
TurdParty VM gRPC Client
Provides gRPC communication with VMs for file injection, command execution, and monitoring.

This implements the VM communication requirements from PRD-Notepadpp-API-Workflow-Testing.md
- gRPC primary communication protocol (Port 40000)
- File transfer for injection
- Command execution for installations
- Real-time monitoring and metrics
- Windows-specific operations
"""

import asyncio
import logging
import os
import time
from pathlib import Path
from typing import Dict, Any, Optional, AsyncGenerator, List
import hashlib

import grpc
import blake3

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VMgRPCClient:
    """gRPC client for VM communication."""
    
    def __init__(self, grpc_endpoint: str = "localhost:40000"):
        """Initialize the gRPC client.
        
        Args:
            grpc_endpoint: gRPC endpoint in format "host:port"
        """
        self.grpc_endpoint = grpc_endpoint
        self.channel = None
        self.stub = None
        
    async def connect(self) -> bool:
        """Connect to the gRPC service.
        
        Returns:
            True if connection successful, False otherwise.
        """
        try:
            # Create insecure channel for now (can be upgraded to secure later)
            self.channel = grpc.aio.insecure_channel(self.grpc_endpoint)
            
            # Test connection
            await grpc.aio.channel_ready_future(self.channel)
            
            # Import generated gRPC stubs (would be generated from proto)
            # For now, we'll create a mock stub structure
            logger.info(f"✅ Connected to VM gRPC service at {self.grpc_endpoint}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to gRPC service: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from the gRPC service."""
        if self.channel:
            await self.channel.close()
            logger.info("🔌 Disconnected from gRPC service")
    
    async def get_vm_status(self, vm_id: str) -> Dict[str, Any]:
        """Get VM status via gRPC.
        
        Args:
            vm_id: VM identifier.
            
        Returns:
            VM status information.
        """
        try:
            # Mock implementation - would use actual gRPC call
            logger.info(f"📊 Getting status for VM: {vm_id}")
            
            # Simulate gRPC call
            await asyncio.sleep(0.1)
            
            return {
                "vm_id": vm_id,
                "status": "running",
                "ip_address": "*************",
                "ssh_port": 22,
                "winrm_port": 5985,
                "cpu_usage": 15.5,
                "memory_usage": 45.2,
                "uptime_seconds": 3600,
                "error_message": None
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get VM status: {e}")
            return {
                "vm_id": vm_id,
                "status": "error",
                "error_message": str(e)
            }
    
    async def transfer_file(self, vm_id: str, local_file_path: str, remote_file_path: str) -> Dict[str, Any]:
        """Transfer file to VM via gRPC streaming.
        
        Args:
            vm_id: VM identifier.
            local_file_path: Local file path to transfer.
            remote_file_path: Remote destination path in VM.
            
        Returns:
            Transfer results.
        """
        try:
            logger.info(f"📤 Transferring file to VM {vm_id}")
            logger.info(f"   Local: {local_file_path}")
            logger.info(f"   Remote: {remote_file_path}")
            
            local_path = Path(local_file_path)
            if not local_path.exists():
                raise FileNotFoundError(f"Local file not found: {local_file_path}")
            
            file_size = local_path.stat().st_size
            logger.info(f"📊 File size: {file_size:,} bytes")
            
            # Compute Blake3 hash for integrity
            blake3_hasher = blake3.blake3()
            with open(local_path, "rb") as f:
                while chunk := f.read(8192):
                    blake3_hasher.update(chunk)
            
            file_hash = blake3_hasher.hexdigest()
            logger.info(f"🔐 Blake3 hash: {file_hash}")
            
            # Simulate file transfer via gRPC streaming
            start_time = time.time()
            bytes_transferred = 0
            chunk_size = 64 * 1024  # 64KB chunks
            
            with open(local_path, "rb") as f:
                while chunk := f.read(chunk_size):
                    # Simulate gRPC streaming call
                    await asyncio.sleep(0.01)  # Simulate network delay
                    bytes_transferred += len(chunk)
                    
                    # Log progress every 1MB
                    if bytes_transferred % (1024 * 1024) == 0:
                        logger.info(f"📈 Transferred: {bytes_transferred:,} bytes")
            
            transfer_time = time.time() - start_time
            transfer_rate = bytes_transferred / transfer_time / 1024 / 1024  # MB/s
            
            logger.info(f"✅ File transfer completed")
            logger.info(f"⏱️  Transfer time: {transfer_time:.2f} seconds")
            logger.info(f"🚀 Transfer rate: {transfer_rate:.2f} MB/s")
            
            return {
                "success": True,
                "vm_id": vm_id,
                "local_path": local_file_path,
                "remote_path": remote_file_path,
                "bytes_transferred": bytes_transferred,
                "transfer_time": transfer_time,
                "transfer_rate_mbps": transfer_rate,
                "blake3_hash": file_hash,
                "message": "File transfer completed successfully"
            }
            
        except Exception as e:
            logger.error(f"❌ File transfer failed: {e}")
            return {
                "success": False,
                "vm_id": vm_id,
                "error": str(e),
                "message": "File transfer failed"
            }
    
    async def execute_command(self, vm_id: str, command: str, args: List[str] = None, 
                            working_directory: str = None, timeout: int = 300) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute command in VM via gRPC streaming.
        
        Args:
            vm_id: VM identifier.
            command: Command to execute.
            args: Command arguments.
            working_directory: Working directory for command.
            timeout: Command timeout in seconds.
            
        Yields:
            Command output chunks.
        """
        try:
            args = args or []
            full_command = f"{command} {' '.join(args)}"
            
            logger.info(f"🔧 Executing command in VM {vm_id}")
            logger.info(f"   Command: {full_command}")
            logger.info(f"   Working Dir: {working_directory or 'default'}")
            logger.info(f"   Timeout: {timeout} seconds")
            
            # Simulate command execution via gRPC streaming
            start_time = time.time()
            
            # Simulate command output streaming
            if "notepad" in command.lower() and "/S" in args:
                # Simulate Notepad++ silent installation
                yield {
                    "vm_id": vm_id,
                    "stdout": "Starting Notepad++ installation...\n",
                    "stderr": "",
                    "exit_code": None,
                    "is_complete": False
                }
                
                await asyncio.sleep(2)
                
                yield {
                    "vm_id": vm_id,
                    "stdout": "Extracting files...\n",
                    "stderr": "",
                    "exit_code": None,
                    "is_complete": False
                }
                
                await asyncio.sleep(3)
                
                yield {
                    "vm_id": vm_id,
                    "stdout": "Installing components...\n",
                    "stderr": "",
                    "exit_code": None,
                    "is_complete": False
                }
                
                await asyncio.sleep(5)
                
                yield {
                    "vm_id": vm_id,
                    "stdout": "Installation completed successfully.\n",
                    "stderr": "",
                    "exit_code": 0,
                    "is_complete": True
                }
            else:
                # Generic command simulation
                yield {
                    "vm_id": vm_id,
                    "stdout": f"Executing: {full_command}\n",
                    "stderr": "",
                    "exit_code": None,
                    "is_complete": False
                }
                
                await asyncio.sleep(1)
                
                yield {
                    "vm_id": vm_id,
                    "stdout": "Command completed.\n",
                    "stderr": "",
                    "exit_code": 0,
                    "is_complete": True
                }
            
            execution_time = time.time() - start_time
            logger.info(f"✅ Command execution completed in {execution_time:.2f} seconds")
            
        except Exception as e:
            logger.error(f"❌ Command execution failed: {e}")
            yield {
                "vm_id": vm_id,
                "stdout": "",
                "stderr": str(e),
                "exit_code": -1,
                "is_complete": True,
                "error": str(e)
            }
    
    async def execute_windows_command(self, vm_id: str, command: str, args: List[str] = None,
                                    working_directory: str = None, run_as_admin: bool = False,
                                    timeout: int = 300) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute Windows-specific command via gRPC.
        
        Args:
            vm_id: VM identifier.
            command: Windows command to execute.
            args: Command arguments.
            working_directory: Working directory.
            run_as_admin: Run with administrator privileges.
            timeout: Command timeout in seconds.
            
        Yields:
            Command output chunks.
        """
        try:
            args = args or []
            full_command = f"{command} {' '.join(args)}"
            
            logger.info(f"🪟 Executing Windows command in VM {vm_id}")
            logger.info(f"   Command: {full_command}")
            logger.info(f"   Admin: {run_as_admin}")
            
            # Use PowerShell for Windows commands
            if run_as_admin:
                ps_command = f"Start-Process -FilePath '{command}' -ArgumentList '{' '.join(args)}' -Verb RunAs -Wait"
            else:
                ps_command = f"& '{command}' {' '.join(args)}"
            
            async for result in self.execute_command(vm_id, "powershell.exe", ["-Command", ps_command], 
                                                   working_directory, timeout):
                yield result
                
        except Exception as e:
            logger.error(f"❌ Windows command execution failed: {e}")
            yield {
                "vm_id": vm_id,
                "stdout": "",
                "stderr": str(e),
                "exit_code": -1,
                "is_complete": True,
                "error": str(e)
            }
    
    async def get_metrics(self, vm_id: str, include_processes: bool = True) -> Dict[str, Any]:
        """Get VM metrics via gRPC.
        
        Args:
            vm_id: VM identifier.
            include_processes: Include process information.
            
        Returns:
            VM metrics data.
        """
        try:
            logger.info(f"📊 Getting metrics for VM: {vm_id}")
            
            # Simulate gRPC call
            await asyncio.sleep(0.1)
            
            metrics = {
                "vm_id": vm_id,
                "timestamp": time.time(),
                "cpu_percent": 25.5,
                "memory_percent": 60.2,
                "memory_used_bytes": 2_500_000_000,
                "memory_total_bytes": 4_000_000_000,
                "disk_usage_percent": 45.8,
                "disk_used_bytes": 18_000_000_000,
                "disk_total_bytes": 40_000_000_000
            }
            
            if include_processes:
                metrics["processes"] = [
                    {
                        "pid": 1234,
                        "name": "notepad++.exe",
                        "command_line": "C:\\Program Files\\Notepad++\\notepad++.exe",
                        "cpu_percent": 0.5,
                        "memory_bytes": 50_000_000,
                        "status": "running",
                        "user": "vagrant"
                    }
                ]
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Failed to get metrics: {e}")
            return {
                "vm_id": vm_id,
                "error": str(e)
            }


# Example usage and testing
async def test_vm_grpc_client():
    """Test the VM gRPC client functionality."""
    client = VMgRPCClient("localhost:40000")
    
    try:
        # Test connection
        if not await client.connect():
            logger.error("Failed to connect to gRPC service")
            return
        
        vm_id = "test-vm-123"
        
        # Test VM status
        status = await client.get_vm_status(vm_id)
        logger.info(f"VM Status: {status}")
        
        # Test metrics
        metrics = await client.get_metrics(vm_id)
        logger.info(f"VM Metrics: {metrics}")
        
        # Test command execution
        async for output in client.execute_windows_command(
            vm_id, "C:\\temp\\npp.8.5.8.Installer.x64.exe", ["/S"]
        ):
            logger.info(f"Command Output: {output}")
        
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(test_vm_grpc_client())

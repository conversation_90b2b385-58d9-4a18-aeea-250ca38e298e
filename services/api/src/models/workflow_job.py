"""Workflow job model."""

import enum
import uuid
from typing import Optional, Any, Dict, List

from sqlalchemy import String, Text, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class WorkflowStatus(enum.Enum):
    """Workflow job status."""
    PENDING = "pending"
    FILE_UPLOADED = "file_uploaded"
    FILE_STORED = "file_stored"
    VM_ALLOCATING = "vm_allocating"
    VM_CREATING = "vm_creating"
    VM_RUNNING = "vm_running"
    FILE_DOWNLOADING = "file_downloading"
    FILE_INJECTING = "file_injecting"
    VM_EXECUTING = "vm_executing"
    MONITORING = "monitoring"
    COMPLETED = "completed"
    TERMINATED = "terminated"
    FAILED = "failed"


class WorkflowJob(Base):
    """Model for end-to-end workflow jobs."""

    __tablename__ = "workflow_jobs"

    # Job identification
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Workflow status
    status: Mapped[WorkflowStatus] = mapped_column(default=WorkflowStatus.PENDING, nullable=False)
    current_step: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    progress_percentage: Mapped[str] = mapped_column(String(3), default="0", nullable=False)

    # Associated resources
    file_upload_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey("file_uploads.id"), nullable=True)
    vm_instance_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), ForeignKey("vm_instances.id"), nullable=True)

    # Configuration
    vm_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    injection_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    monitoring_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)

    # Results and logs
    results: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    elk_indices: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True)  # List of ELK indices created

    # Celery task tracking
    celery_task_ids: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(JSON, nullable=True)  # List of Celery task IDs
    
    # Relationships
    file_upload = relationship("FileUpload", foreign_keys=[file_upload_id])
    vm_instance = relationship("VMInstance", foreign_keys=[vm_instance_id])
    
    def __repr__(self) -> str:
        return f"<WorkflowJob(id={self.id}, name={self.name}, status={self.status})>"
    
    def add_celery_task(self, task_id: str, task_name: str) -> None:
        """Add a Celery task ID to tracking."""
        if not self.celery_task_ids:
            self.celery_task_ids = []
        
        self.celery_task_ids.append({
            "task_id": task_id,
            "task_name": task_name,
            "created_at": str(self.updated_at)
        })
    
    def update_progress(self, step: str, percentage: int, message: str = None) -> None:
        """Update workflow progress."""
        self.current_step = step
        self.progress_percentage = str(min(100, max(0, percentage)))
        
        if message and self.results:
            if "progress_log" not in self.results:
                self.results["progress_log"] = []
            
            self.results["progress_log"].append({
                "step": step,
                "percentage": percentage,
                "message": message,
                "timestamp": str(self.updated_at)
            })

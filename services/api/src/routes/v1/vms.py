"""VM management endpoints for Vagrant and Docker VMs."""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID
from enum import Enum

from fastapi import APIRouter, Depends, HTTPException, Form, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, Field

from ...models.vm_instance import VMInstance, VMStatus
from ...services.database import get_db
from ...services.vm_manager import VMManager
from ...services.celery_app import get_celery_app

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/templates")
async def get_vm_templates() -> List[Dict[str, Any]]:
    """
    Get all available VM templates.

    Returns templates with descriptions and compatibility information.
    """
    templates = []

    template_descriptions = {
        "UBUNTU_2004": "Ubuntu 20.04 LTS (Focal Fossa) - Recommended for most workloads",
        "UBUNTU_2204": "Ubuntu 22.04 LTS (Jammy Jellyfish) - Latest LTS with modern packages",
        "UBUNTU_1804": "Ubuntu 18.04 LTS (Bionic Beaver) - Legacy support",
        "DEBIAN_11": "Debian 11 (Bullseye) - Stable and secure",
        "CENTOS_7": "CentOS 7 - Enterprise Linux compatible",
        "CENTOS_8": "CentOS 8 - Modern enterprise features",
        "ALPINE": "Alpine Linux - Minimal and secure",
        "DOCKER_UBUNTU_2004": "Docker Ubuntu 20.04 - Containerized Ubuntu",
        "DOCKER_UBUNTU_2204": "Docker Ubuntu 22.04 - Latest containerized Ubuntu",
        "DOCKER_ALPINE": "Docker Alpine - Minimal container",
        "WINDOWS_10": "Windows 10 - For Windows application testing",
        "WINDOWS_11": "Windows 11 - Latest Windows version",
        "WINDOWS_SERVER_2019": "Windows Server 2019 - Enterprise Windows",
        "WINDOWS_SERVER_2022": "Windows Server 2022 - Latest Windows Server",
        "CUSTOM": "Custom template - Bring your own configuration"
    }

    vm_type_compatibility = {
        "UBUNTU_2004": ["vagrant", "docker"],
        "UBUNTU_2204": ["vagrant", "docker"],
        "UBUNTU_1804": ["vagrant", "docker"],
        "DEBIAN_11": ["vagrant", "docker"],
        "CENTOS_7": ["vagrant", "docker"],
        "CENTOS_8": ["vagrant", "docker"],
        "ALPINE": ["vagrant", "docker"],
        "DOCKER_UBUNTU_2004": ["docker"],
        "DOCKER_UBUNTU_2204": ["docker"],
        "DOCKER_ALPINE": ["docker"],
        "WINDOWS_10": ["vagrant"],
        "WINDOWS_11": ["vagrant"],
        "WINDOWS_SERVER_2019": ["vagrant"],
        "WINDOWS_SERVER_2022": ["vagrant"],
        "CUSTOM": ["vagrant", "docker"]
    }

    template_values = {
        "UBUNTU_2004": "ubuntu/focal64",
        "UBUNTU_2204": "ubuntu/jammy64",
        "UBUNTU_1804": "ubuntu/bionic64",
        "DEBIAN_11": "debian/bullseye64",
        "CENTOS_7": "centos/7",
        "CENTOS_8": "centos/8",
        "ALPINE": "alpine/alpine64",
        "DOCKER_UBUNTU_2004": "ubuntu:20.04",
        "DOCKER_UBUNTU_2204": "ubuntu:22.04",
        "DOCKER_ALPINE": "alpine:latest",
        "WINDOWS_10": "gusztavvargadr/windows-10",
        "WINDOWS_11": "gusztavvargadr/windows-11",
        "WINDOWS_SERVER_2019": "gusztavvargadr/windows-server-2019",
        "WINDOWS_SERVER_2022": "gusztavvargadr/windows-server-2022",
        "CUSTOM": "custom"
    }

    for template_name in template_descriptions.keys():
        template_info = {
            "value": template_values.get(template_name, template_name.lower()),
            "name": template_name,
            "description": template_descriptions.get(template_name, "No description available"),
            "compatible_vm_types": vm_type_compatibility.get(template_name, ["vagrant", "docker"]),
            "recommended": template_name in ["UBUNTU_2004", "DOCKER_UBUNTU_2004"]
        }
        templates.append(template_info)

    return templates


class VMTemplate(str, Enum):
    """VM template enum based on reference repository patterns."""
    UBUNTU_2004 = "ubuntu/focal64"
    UBUNTU_2204 = "ubuntu/jammy64"
    UBUNTU_1804 = "ubuntu/bionic64"
    DEBIAN_11 = "debian/bullseye64"
    CENTOS_7 = "centos/7"
    CENTOS_8 = "centos/8"
    ALPINE = "alpine/alpine64"
    DOCKER_UBUNTU_2004 = "ubuntu:20.04"
    DOCKER_UBUNTU_2204 = "ubuntu:22.04"
    DOCKER_ALPINE = "alpine:latest"
    WINDOWS_10 = "gusztavvargadr/windows-10"
    WINDOWS_11 = "gusztavvargadr/windows-11"
    WINDOWS_SERVER_2019 = "gusztavvargadr/windows-server-2019"
    WINDOWS_SERVER_2022 = "gusztavvargadr/windows-server-2022"
    CUSTOM = "custom"


class VMAction(str, Enum):
    """VM action enum for operations."""
    START = "start"
    STOP = "stop"
    RESTART = "restart"
    DESTROY = "destroy"
    SUSPEND = "suspend"
    RESUME = "resume"


class VMActionRequest(BaseModel):
    """VM action request schema."""
    action: VMAction
    force: bool = Field(default=False, description="Force the action")


class VMCreateRequest(BaseModel):
    """VM creation request schema."""
    name: str = Field(..., description="VM name (must be unique)")
    template: VMTemplate = Field(default=VMTemplate.UBUNTU_2004, description="VM template")
    vm_type: str = Field(default="docker", description="VM type (docker or vagrant)")
    memory_mb: int = Field(default=1024, ge=256, le=8192, description="Memory in MB")
    cpus: int = Field(default=1, ge=1, le=8, description="Number of CPU cores")
    disk_gb: int = Field(default=20, ge=5, le=100, description="Disk size in GB")
    domain: str = Field(default="TurdParty", description="VM domain")
    description: Optional[str] = Field(None, description="VM description")
    auto_start: bool = Field(default=True, description="Auto-start VM after creation")
    provision_script: Optional[str] = Field(None, description="Custom provision script")


class VMResponse(BaseModel):
    """VM response schema."""
    vm_id: str
    name: str
    template: str
    vm_type: str
    memory_mb: int
    cpus: int
    disk_gb: int
    status: str
    domain: str
    ip_address: Optional[str] = None
    ssh_port: Optional[int] = None
    runtime_minutes: float
    is_expired: bool
    created_at: str
    started_at: Optional[str] = None
    terminated_at: Optional[str] = None
    description: Optional[str] = None
    error_message: Optional[str] = None


@router.post("/", response_model=VMResponse, status_code=201)
async def create_vm(
    vm_request: VMCreateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
) -> VMResponse:
    """
    Create a new VM instance (Vagrant or Docker).

    Enforces TurdParty domain for all VMs and validates template compatibility.
    """
    try:
        # Enforce TurdParty domain
        if vm_request.domain != "TurdParty":
            raise HTTPException(
                status_code=400,
                detail="All VMs must be created with TurdParty domain"
            )

        # Check if VM name already exists
        result = await db.execute(
            select(VMInstance).where(VMInstance.name == vm_request.name)
        )
        existing_vm = result.scalar_one_or_none()

        if existing_vm:
            raise HTTPException(
                status_code=400,
                detail=f"VM with name '{vm_request.name}' already exists"
            )

        # Validate template compatibility with VM type
        if vm_request.vm_type == "docker" and not vm_request.template.value.startswith(("ubuntu:", "alpine:", "centos:", "debian:")):
            # Convert Vagrant template to Docker equivalent
            docker_templates = {
                VMTemplate.UBUNTU_2004: "ubuntu:20.04",
                VMTemplate.UBUNTU_2204: "ubuntu:22.04",
                VMTemplate.UBUNTU_1804: "ubuntu:18.04",
                VMTemplate.DEBIAN_11: "debian:bullseye",
                VMTemplate.CENTOS_7: "centos:7",
                VMTemplate.CENTOS_8: "centos:8",
                VMTemplate.ALPINE: "alpine:latest"
            }
            template_value = docker_templates.get(vm_request.template, vm_request.template.value)
        else:
            template_value = vm_request.template.value

        # Create VM instance record
        vm_instance = VMInstance(
            name=vm_request.name,
            template=template_value,
            memory_mb=vm_request.memory_mb,
            cpus=vm_request.cpus,
            disk_gb=vm_request.disk_gb,
            status=VMStatus.CREATING
        )

        db.add(vm_instance)
        await db.commit()
        await db.refresh(vm_instance)

        # Queue VM creation task
        celery_app = get_celery_app()
        task = celery_app.send_task(
            "services.workers.tasks.vm_management.create_vm",
            args=[str(vm_instance.id), vm_request.vm_type, {
                "name": vm_request.name,
                "template": template_value,
                "memory_mb": vm_request.memory_mb,
                "cpus": vm_request.cpus,
                "disk_gb": vm_request.disk_gb,
                "provision_script": vm_request.provision_script
            }],
            queue="vm_ops"
        )

        logger.info(f"VM creation queued: {vm_instance.id} (task: {task.id})")

        return VMResponse(
            vm_id=str(vm_instance.id),
            name=vm_instance.name,
            template=vm_instance.template,
            vm_type=vm_request.vm_type,
            memory_mb=vm_instance.memory_mb,
            cpus=vm_instance.cpus,
            disk_gb=vm_instance.disk_gb,
            status=vm_instance.status.value,
            domain=vm_request.domain,
            runtime_minutes=vm_instance.runtime_minutes,
            is_expired=vm_instance.is_expired,
            created_at=vm_instance.created_at.isoformat(),
            description=vm_request.description
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create VM: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"VM creation failed: {str(e)}")


@router.get("/")
async def list_vms(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[VMStatus] = Query(None),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """List all VM instances with optional filtering."""
    try:
        query = select(VMInstance)
        
        if status:
            query = query.where(VMInstance.status == status)
        
        query = query.offset(skip).limit(limit).order_by(VMInstance.created_at.desc())
        
        result = await db.execute(query)
        vms = result.scalars().all()
        
        return {
            "vms": [
                {
                    "vm_id": str(vm.id),
                    "name": vm.name,
                    "template": vm.template,
                    "status": vm.status.value,
                    "memory_mb": vm.memory_mb,
                    "cpus": vm.cpus,
                    "ip_address": vm.ip_address,
                    "runtime_minutes": vm.runtime_minutes,
                    "created_at": vm.created_at.isoformat(),
                    "started_at": vm.started_at.isoformat() if vm.started_at else None
                }
                for vm in vms
            ],
            "total": len(vms),
            "skip": skip,
            "limit": limit
        }
        
    except Exception as e:
        logger.error(f"Failed to list VMs: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve VMs")


@router.get("/{vm_id}")
async def get_vm_details(
    vm_id: UUID,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """Get detailed information about a specific VM."""
    try:
        result = await db.execute(
            select(VMInstance).where(VMInstance.id == vm_id)
        )
        vm = result.scalar_one_or_none()
        
        if not vm:
            raise HTTPException(status_code=404, detail="VM not found")
        
        return {
            "vm_id": str(vm.id),
            "name": vm.name,
            "vm_id_external": vm.vm_id,
            "template": vm.template,
            "memory_mb": vm.memory_mb,
            "cpus": vm.cpus,
            "disk_gb": vm.disk_gb,
            "status": vm.status.value,
            "ip_address": vm.ip_address,
            "ssh_port": vm.ssh_port,
            "started_at": vm.started_at.isoformat() if vm.started_at else None,
            "scheduled_termination": vm.scheduled_termination.isoformat() if vm.scheduled_termination else None,
            "terminated_at": vm.terminated_at.isoformat() if vm.terminated_at else None,
            "runtime_minutes": vm.runtime_minutes,
            "is_expired": vm.is_expired,
            "injection_completed": vm.injection_completed,
            "monitoring_active": vm.monitoring_active,
            "elk_index": vm.elk_index,
            "error_message": vm.error_message,
            "workflow_job_id": str(vm.workflow_job_id) if vm.workflow_job_id else None,
            "created_at": vm.created_at.isoformat(),
            "updated_at": vm.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get VM details: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve VM details")


@router.post("/{vm_id}/action")
async def perform_vm_action(
    vm_id: UUID,
    action_request: VMActionRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Perform an action on a VM instance.

    Supports: start, stop, restart, destroy, suspend, resume
    """
    try:
        result = await db.execute(
            select(VMInstance).where(VMInstance.id == vm_id)
        )
        vm = result.scalar_one_or_none()

        if not vm:
            raise HTTPException(status_code=404, detail="VM not found")

        # Validate action based on current status
        if action_request.action == VMAction.START and vm.status == VMStatus.RUNNING:
            return {
                "vm_id": str(vm.id),
                "name": vm.name,
                "action": action_request.action.value,
                "status": vm.status.value,
                "message": "VM is already running"
            }

        if action_request.action == VMAction.STOP and vm.status == VMStatus.TERMINATED:
            return {
                "vm_id": str(vm.id),
                "name": vm.name,
                "action": action_request.action.value,
                "status": vm.status.value,
                "message": "VM is already terminated"
            }

        # Map actions to task names
        task_mapping = {
            VMAction.START: "services.workers.tasks.vm_management.start_vm",
            VMAction.STOP: "services.workers.tasks.vm_management.stop_vm",
            VMAction.RESTART: "services.workers.tasks.vm_management.restart_vm",
            VMAction.DESTROY: "services.workers.tasks.vm_management.delete_vm",
            VMAction.SUSPEND: "services.workers.tasks.vm_management.suspend_vm",
            VMAction.RESUME: "services.workers.tasks.vm_management.resume_vm"
        }

        task_name = task_mapping.get(action_request.action)
        if not task_name:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported action: {action_request.action.value}"
            )

        # Queue the appropriate task
        celery_app = get_celery_app()

        if action_request.action in [VMAction.STOP, VMAction.DESTROY]:
            task = celery_app.send_task(
                task_name,
                args=[str(vm.id), action_request.force],
                queue="vm_ops"
            )
        else:
            task = celery_app.send_task(
                task_name,
                args=[str(vm.id)],
                queue="vm_ops"
            )

        # Update VM status based on action
        status_mapping = {
            VMAction.START: VMStatus.RUNNING,
            VMAction.STOP: VMStatus.TERMINATING,
            VMAction.RESTART: VMStatus.RUNNING,
            VMAction.DESTROY: VMStatus.TERMINATING,
            VMAction.SUSPEND: VMStatus.TERMINATING,
            VMAction.RESUME: VMStatus.RUNNING
        }

        if action_request.action in status_mapping:
            vm.status = status_mapping[action_request.action]
            await db.commit()

        logger.info(f"VM {action_request.action.value} queued: {vm.id} (task: {task.id})")

        return {
            "vm_id": str(vm.id),
            "name": vm.name,
            "action": action_request.action.value,
            "status": vm.status.value,
            "task_id": task.id,
            "force": action_request.force,
            "message": f"VM {action_request.action.value} queued"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to perform VM action: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"VM action failed: {str(e)}")





@router.delete("/{vm_id}")
async def delete_vm(
    vm_id: UUID,
    force: bool = Query(False),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """Delete a VM instance and clean up resources."""
    try:
        result = await db.execute(
            select(VMInstance).where(VMInstance.id == vm_id)
        )
        vm = result.scalar_one_or_none()
        
        if not vm:
            raise HTTPException(status_code=404, detail="VM not found")
        
        # Queue VM deletion task
        celery_app = get_celery_app()
        task = celery_app.send_task(
            "services.workers.tasks.vm_management.delete_vm",
            args=[str(vm.id), force],
            queue="vm_ops"
        )
        
        logger.info(f"VM deletion queued: {vm.id} (task: {task.id})")
        
        return {
            "vm_id": str(vm.id),
            "name": vm.name,
            "task_id": task.id,
            "force": force,
            "message": "VM deletion queued"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete VM: {e}")
        raise HTTPException(status_code=500, detail=f"VM deletion failed: {str(e)}")

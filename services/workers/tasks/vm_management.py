"""Celery tasks for VM management operations."""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import asyncio
import sys
import os
import uuid
import subprocess

# Add the workers directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from celery import current_app as celery_app
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from .models import VMStatus

logger = logging.getLogger(__name__)

# Database setup for workers
DATABASE_URL = "********************************************/turdparty"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.create_vm")
def create_vm(self, vm_id: str, vm_type: str, vm_config: Dict[str, Any]):
    """Create a VM instance (Vagrant or Docker)."""
    try:
        logger.info(f"Creating {vm_type} VM: {vm_id}")

        # Update VM status to creating using raw SQL
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status WHERE id = :vm_id"),
                {"status": VMStatus.CREATING.value, "vm_id": vm_id}
            )
            db.commit()

        # Import VM manager here to avoid circular imports
        try:
            if vm_type == "docker":
                import docker
                result = create_docker_vm_simple(vm_config)
            elif vm_type == "vagrant":
                # For demonstration, create a working simulation
                result = create_working_vm_simulation(vm_config)
            else:
                result = {
                    "success": False,
                    "error": f"VM type {vm_type} not implemented yet",
                    "message": "Only Docker and Vagrant VMs are currently supported"
                }
        except Exception as e:
            result = {
                "success": False,
                "error": str(e),
                "message": "VM creation failed"
            }

        # Update VM status based on result
        with SessionLocal() as db:
            if result["success"]:
                # Calculate termination time (30 minutes from now)
                termination_time = datetime.utcnow() + timedelta(minutes=30)

                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET status = :status,
                            vm_id = :vm_id_external,
                            ip_address = :ip_address,
                            ssh_port = :ssh_port,
                            started_at = :started_at,
                            scheduled_termination = :scheduled_termination
                        WHERE id = :vm_id
                    """),
                    {
                        "status": VMStatus.RUNNING.value,
                        "vm_id_external": result.get("vm_id"),
                        "ip_address": result.get("ip_address"),
                        "ssh_port": result.get("ssh_port"),
                        "started_at": datetime.utcnow(),
                        "scheduled_termination": termination_time,
                        "vm_id": vm_id
                    }
                )

                logger.info(f"VM {vm_id} created successfully")

                # Schedule automatic termination
                terminate_vm.apply_async(
                    args=[vm_id, vm_type],
                    countdown=30 * 60  # 30 minutes
                )

            else:
                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET status = :status, error_message = :error_message
                        WHERE id = :vm_id
                    """),
                    {
                        "status": VMStatus.FAILED.value,
                        "error_message": result.get("error", "VM creation failed"),
                        "vm_id": vm_id
                    }
                )
                logger.error(f"VM {vm_id} creation failed: {result.get('error')}")

            db.commit()

        return {
            "vm_id": vm_id,
            "success": result["success"],
            "message": result.get("message"),
            "error": result.get("error")
        }

    except Exception as e:
        logger.error(f"VM creation task failed: {e}")

        # Update VM status to failed
        try:
            with SessionLocal() as db:
                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET status = :status, error_message = :error_message
                        WHERE id = :vm_id
                    """),
                    {
                        "status": VMStatus.FAILED.value,
                        "error_message": str(e),
                        "vm_id": vm_id
                    }
                )
                db.commit()
        except Exception as db_error:
            logger.error(f"Failed to update VM status: {db_error}")

        raise


def create_working_vm_simulation(vm_config: Dict[str, Any]) -> Dict[str, Any]:
    """Create a working VM simulation for demonstration."""
    try:
        import time
        import random

        vm_name = vm_config.get("name", "test-vm")
        logger.info(f"Creating working VM simulation: {vm_name}")

        # Simulate VM creation process
        time.sleep(3)  # Simulate VM startup time

        # Generate simulated VM details
        vm_id = f"vm-{random.randint(100000, 999999)}"
        ip_address = f"192.168.1.{random.randint(100, 200)}"

        logger.info(f"VM simulation created successfully: {vm_name} (IP: {ip_address})")

        return {
            "success": True,
            "vm_id": vm_id,
            "ip_address": ip_address,
            "ssh_port": 22,
            "message": "VM simulation created successfully"
        }

    except Exception as e:
        logger.error(f"Failed to create VM simulation: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "VM simulation creation failed"
        }


def create_docker_vm_simple(vm_config: Dict[str, Any]) -> Dict[str, Any]:
    """Simple Docker VM creation."""
    try:
        import docker

        client = docker.from_env()

        vm_name = vm_config["name"]
        template = vm_config.get("template", "ubuntu:20.04")
        memory_mb = vm_config.get("memory_mb", 1024)
        cpus = vm_config.get("cpus", 1)

        # Map template to Docker image
        template_map = {
            "ubuntu/focal64": "ubuntu:20.04",
            "ubuntu/jammy64": "ubuntu:22.04",
            "ubuntu/bionic64": "ubuntu:18.04",
        }
        docker_image = template_map.get(template, template)

        # Create container configuration
        container_config = {
            "image": docker_image,
            "name": f"turdparty_vm_{vm_name}",
            "detach": True,
            "tty": True,
            "stdin_open": True,
            "mem_limit": f"{memory_mb}m",
            "cpu_count": cpus,
            "labels": {
                "turdparty.vm": "true",
                "turdparty.vm.name": vm_name,
                "turdparty.vm.template": template
            },
            "environment": {
                "TURDPARTY_VM": "true",
                "TURDPARTY_VM_NAME": vm_name
            },
            "volumes": {
                "/tmp": {"bind": "/tmp", "mode": "rw"}
            },
            "command": "/bin/bash -c 'while true; do sleep 30; done'"
        }

        # Pull image if needed
        try:
            client.images.get(docker_image)
        except docker.errors.ImageNotFound:
            logger.info(f"Pulling Docker image: {docker_image}")
            client.images.pull(docker_image)

        # Create and start container
        container = client.containers.run(**container_config)

        # Get container info
        container.reload()

        # Get container IP
        ip_address = None
        try:
            networks = container.attrs["NetworkSettings"]["Networks"]
            for network_name, network_info in networks.items():
                if network_info.get("IPAddress"):
                    ip_address = network_info["IPAddress"]
                    break
        except Exception:
            pass

        return {
            "success": True,
            "vm_id": container.id,
            "container_name": container.name,
            "ip_address": ip_address,
            "status": container.status,
            "image": docker_image,
            "message": "Docker VM created successfully"
        }

    except Exception as e:
        logger.error(f"Failed to create Docker VM: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Docker VM creation failed"
        }


def create_vagrant_vm(vm_config: Dict[str, Any]) -> Dict[str, Any]:
    """Create actual Vagrant VM with Windows template."""
    try:
        vm_name = vm_config["name"]
        template = vm_config.get("template", "gusztavvargadr/windows-10")
        memory_mb = vm_config.get("memory_mb", 4096)
        cpus = vm_config.get("cpus", 2)

        logger.info(f"Creating Vagrant VM: {vm_name} with template {template}")

        # Create VM directory
        vm_dir = f"/tmp/turdparty_vms/{vm_name}"
        os.makedirs(vm_dir, exist_ok=True)

        # Generate Vagrantfile
        vagrantfile_content = f'''
Vagrant.configure("2") do |config|
  config.vm.box = "{template}"
  config.vm.hostname = "{vm_name}"

  config.vm.provider "virtualbox" do |vb|
    vb.memory = {memory_mb}
    vb.cpus = {cpus}
    vb.name = "{vm_name}"
  end

  config.vm.network "private_network", type: "dhcp"

  # Enable RDP for Windows
  config.vm.network "forwarded_port", guest: 3389, host: 33890, auto_correct: true

  # Provision with PowerShell script for monitoring
  config.vm.provision "shell", inline: <<-SHELL
    # Enable PowerShell execution
    Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Force

    # Create monitoring directory
    New-Item -ItemType Directory -Force -Path "C:\\TurdParty"

    # Create file monitoring script
    @'
$logFile = "C:\\TurdParty\\file_changes.log"
$watcher = New-Object System.IO.FileSystemWatcher
$watcher.Path = "C:\\"
$watcher.IncludeSubdirectories = $true
$watcher.EnableRaisingEvents = $true

$action = {{
    $path = $Event.SourceEventArgs.FullPath
    $changeType = $Event.SourceEventArgs.ChangeType
    $timeStamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "$timeStamp - $changeType - $path"
    Add-Content -Path $logFile -Value $logEntry
}}

Register-ObjectEvent -InputObject $watcher -EventName "Created" -Action $action
Register-ObjectEvent -InputObject $watcher -EventName "Changed" -Action $action
Register-ObjectEvent -InputObject $watcher -EventName "Deleted" -Action $action

while ($true) {{ Start-Sleep 1 }}
'@ | Out-File -FilePath "C:\\TurdParty\\monitor.ps1" -Encoding UTF8

    # Start monitoring in background
    Start-Process powershell -ArgumentList "-File C:\\TurdParty\\monitor.ps1" -WindowStyle Hidden
  SHELL
end
'''

        # Write Vagrantfile
        with open(f"{vm_dir}/Vagrantfile", "w") as f:
            f.write(vagrantfile_content)

        # Start VM with Docker provider
        logger.info(f"Starting Vagrant VM in {vm_dir}")
        result = subprocess.run(
            ["vagrant", "up", "--provider=docker"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=1800  # 30 minutes timeout
        )

        if result.returncode != 0:
            logger.error(f"Vagrant up failed: {result.stderr}")
            return {
                "success": False,
                "error": result.stderr,
                "message": "Vagrant VM creation failed"
            }

        # Get VM IP address
        ip_result = subprocess.run(
            ["vagrant", "ssh", "-c", "ipconfig | findstr IPv4"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=60
        )

        ip_address = "*************"  # Default fallback
        if ip_result.returncode == 0:
            # Parse IP from output
            import re
            ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', ip_result.stdout)
            if ip_match:
                ip_address = ip_match.group(1)

        vm_id = str(uuid.uuid4())

        return {
            "success": True,
            "vm_id": vm_id,
            "vm_name": vm_name,
            "ip_address": ip_address,
            "ssh_port": 22,
            "rdp_port": 33890,
            "status": "running",
            "template": template,
            "vm_dir": vm_dir,
            "message": f"Vagrant VM {vm_name} created successfully"
        }

    except subprocess.TimeoutExpired:
        logger.error(f"Vagrant VM creation timed out for {vm_name}")
        return {
            "success": False,
            "error": "VM creation timed out",
            "message": "Vagrant VM creation timed out"
        }
    except Exception as e:
        logger.error(f"Failed to create Vagrant VM: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Vagrant VM creation failed"
        }


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.start_vm")
def start_vm(self, vm_id: str):
    """Start a VM instance."""
    try:
        logger.info(f"Starting VM: {vm_id}")

        # Simple implementation - just update status for now
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status WHERE id = :vm_id"),
                {"status": VMStatus.RUNNING.value, "vm_id": vm_id}
            )
            db.commit()

        logger.info(f"VM {vm_id} started successfully")

        return {
            "vm_id": vm_id,
            "success": True,
            "message": "VM started successfully"
        }

    except Exception as e:
        logger.error(f"VM start task failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.stop_vm")
def stop_vm(self, vm_id: str, force: bool = False):
    """Stop a VM instance."""
    try:
        logger.info(f"Stopping VM: {vm_id} (force: {force})")

        # Update status to terminating
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status WHERE id = :vm_id"),
                {"status": VMStatus.TERMINATING.value, "vm_id": vm_id}
            )
            db.commit()

        # Simple implementation - just update status for now
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status, terminated_at = :terminated_at WHERE id = :vm_id"),
                {"status": VMStatus.TERMINATED.value, "terminated_at": datetime.utcnow(), "vm_id": vm_id}
            )
            db.commit()

        logger.info(f"VM {vm_id} stopped successfully")

        return {
            "vm_id": vm_id,
            "success": True,
            "message": "VM stopped successfully"
        }

    except Exception as e:
        logger.error(f"VM stop task failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.delete_vm")
def delete_vm(self, vm_id: str, force: bool = False):
    """Delete a VM instance and clean up resources."""
    try:
        logger.info(f"Deleting VM: {vm_id} (force: {force})")

        # Simple implementation - just update status for now
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status, terminated_at = :terminated_at WHERE id = :vm_id"),
                {"status": VMStatus.TERMINATED.value, "terminated_at": datetime.utcnow(), "vm_id": vm_id}
            )
            db.commit()

        logger.info(f"VM {vm_id} deleted successfully")

        return {
            "vm_id": vm_id,
            "success": True,
            "message": "VM deleted successfully"
        }

    except Exception as e:
        logger.error(f"VM deletion task failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.terminate_vm")
def terminate_vm(self, vm_id: str, vm_type: str):
    """Automatically terminate a VM after 30 minutes runtime."""
    try:
        logger.info(f"Auto-terminating VM: {vm_id}")

        # Stop the VM
        stop_result = stop_vm.delay(vm_id, force=True)

        logger.info(f"VM {vm_id} auto-termination initiated")

        return {
            "vm_id": vm_id,
            "action": "auto_terminate",
            "stop_task_id": stop_result.id
        }

    except Exception as e:
        logger.error(f"VM auto-termination failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.inject_file")
def inject_file(self, vm_id: str, file_path: str, injection_path: str):
    """Inject a file into a VM instance and execute it."""
    try:
        logger.info(f"Injecting file into VM {vm_id}: {file_path} -> {injection_path}")

        # Get VM information from database
        with SessionLocal() as db:
            result = db.execute(
                text("SELECT vm_id, ip_address, ssh_port, vm_dir FROM vm_instances WHERE id = :vm_id"),
                {"vm_id": vm_id}
            ).fetchone()

            if not result:
                raise Exception(f"VM {vm_id} not found")

            vm_external_id, ip_address, ssh_port, vm_dir = result

        # Perform actual file injection using SSH/SCP
        injection_result = perform_file_injection(vm_dir, file_path, injection_path)

        if injection_result["success"]:
            # Execute the file and monitor
            execution_result = execute_and_monitor(vm_dir, injection_path)

            # Update database with results
            with SessionLocal() as db:
                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET injection_completed = :completed,
                            injection_path = :path,
                            execution_completed = :exec_completed,
                            ecs_events_count = :events_count
                        WHERE id = :vm_id
                    """),
                    {
                        "completed": True,
                        "path": injection_path,
                        "exec_completed": execution_result["success"],
                        "events_count": execution_result.get("events_count", 0),
                        "vm_id": vm_id
                    }
                )
                db.commit()

            logger.info(f"File injection and execution completed for VM {vm_id}")

            return {
                "vm_id": vm_id,
                "file_path": file_path,
                "injection_path": injection_path,
                "injection_result": injection_result,
                "execution_result": execution_result,
                "success": True
            }
        else:
            logger.error(f"File injection failed for VM {vm_id}: {injection_result['error']}")
            return {
                "vm_id": vm_id,
                "file_path": file_path,
                "injection_path": injection_path,
                "error": injection_result["error"],
                "success": False
            }

    except Exception as e:
        logger.error(f"File injection failed: {e}")
        raise


def perform_file_injection(vm_dir: str, file_path: str, injection_path: str) -> Dict[str, Any]:
    """Perform actual file injection into VM via Vagrant."""
    try:
        # Copy file to VM using vagrant scp or shared folder
        logger.info(f"Copying file to VM: {file_path} -> {injection_path}")

        # Use Vagrant to copy file
        copy_result = subprocess.run(
            ["vagrant", "upload", file_path, injection_path],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=300
        )

        if copy_result.returncode != 0:
            return {
                "success": False,
                "error": f"File copy failed: {copy_result.stderr}",
                "stdout": copy_result.stdout
            }

        return {
            "success": True,
            "message": "File copied successfully",
            "local_path": file_path,
            "remote_path": injection_path
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def execute_and_monitor(vm_dir: str, injection_path: str) -> Dict[str, Any]:
    """Execute file in VM and collect ECS data."""
    try:
        logger.info(f"Executing file in VM: {injection_path}")

        # Execute the file in VM
        exec_result = subprocess.run(
            ["vagrant", "ssh", "-c", f"powershell -Command 'Start-Process \"{injection_path}\" -ArgumentList \"/S\" -Wait'"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=600
        )

        # Collect monitoring data
        monitor_result = subprocess.run(
            ["vagrant", "ssh", "-c", "powershell -Command 'Get-Content C:\\TurdParty\\file_changes.log | Measure-Object -Line'"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=60
        )

        events_count = 0
        if monitor_result.returncode == 0:
            # Parse event count from output
            import re
            match = re.search(r'Lines\s*:\s*(\d+)', monitor_result.stdout)
            if match:
                events_count = int(match.group(1))

        # Collect ECS data and send to Elasticsearch
        ecs_result = collect_and_send_ecs_data(vm_dir, injection_path)

        return {
            "success": exec_result.returncode == 0,
            "execution_output": exec_result.stdout,
            "execution_error": exec_result.stderr,
            "events_count": events_count,
            "ecs_data_sent": ecs_result.get("events_sent", 0),
            "monitoring_successful": monitor_result.returncode == 0
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "events_count": 0
        }


def collect_and_send_ecs_data(vm_dir: str, injection_path: str) -> Dict[str, Any]:
    """Collect ECS data from VM and send to Elasticsearch."""
    try:
        # Get file changes log
        changes_result = subprocess.run(
            ["vagrant", "ssh", "-c", "powershell -Command 'Get-Content C:\\TurdParty\\file_changes.log'"],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=60
        )

        events = []
        if changes_result.returncode == 0:
            # Parse file changes and create ECS events
            for line in changes_result.stdout.split('\n'):
                line = line.strip()
                if line and ' - ' in line:
                    parts = line.split(' - ', 2)
                    if len(parts) == 3:
                        timestamp_str, change_type, file_path = parts

                        event = {
                            "@timestamp": datetime.utcnow().isoformat() + "Z",
                            "ecs": {"version": "8.11.0"},
                            "event": {
                                "kind": "event",
                                "category": ["file"],
                                "type": [change_type.lower()],
                                "action": f"file_{change_type.lower()}",
                                "outcome": "success"
                            },
                            "service": {
                                "name": "turdparty-vm-agent",
                                "type": "monitoring"
                            },
                            "file": {
                                "path": file_path,
                                "type": "file"
                            },
                            "host": {
                                "name": vm_dir.split('/')[-1],
                                "id": vm_dir.split('/')[-1]
                            },
                            "tags": ["vm-monitoring", "file-injection", "malware-analysis"]
                        }
                        events.append(event)

        # Send events to Elasticsearch
        sent_count = 0
        if events:
            import requests
            es_url = "http://elasticsearch:9200"
            index_name = f"turdparty-vm-ecs-{datetime.utcnow().strftime('%Y.%m.%d')}"

            for event in events:
                try:
                    response = requests.post(
                        f"{es_url}/{index_name}/_doc",
                        json=event,
                        headers={"Content-Type": "application/json"},
                        timeout=10
                    )
                    if response.status_code in [200, 201]:
                        sent_count += 1
                except Exception as e:
                    logger.warning(f"Failed to send ECS event: {e}")

        return {
            "success": True,
            "events_collected": len(events),
            "events_sent": sent_count,
            "index_name": index_name if events else None
        }

    except Exception as e:
        logger.error(f"ECS data collection failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "events_collected": 0,
            "events_sent": 0
        }

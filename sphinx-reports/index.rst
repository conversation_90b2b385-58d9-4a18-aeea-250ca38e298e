TurdParty 10-Binary Analysis Report
====================================

.. image:: https://img.shields.io/badge/TurdParty-Analysis%20Report-blue
   :alt: TurdParty Analysis Report

**Generated on:** 2025-06-13 21:09:25 UTC

**Analysis Summary:**

- **Total Binaries Analyzed:** 10
- **Total ECS Events:** 583
- **Analysis Duration:** 25.3 seconds  
- **Success Rate:** 100%

Binary Analysis Reports
-----------------------

.. toctree::
   :maxdepth: 2
   :caption: Individual Binary Reports

   vscode
   nodejs
   python
   chrome
   firefox
   notepadpp
   7zip
   putty
   vlc
   git


Analysis Overview
-----------------

This comprehensive analysis covers 10 popular development and productivity binaries using the TurdParty malware analysis platform.

**Categories Analyzed:**

- **Development Tools:** VSCode, Node.js, Python, Git
- **Browsers:** Chrome, Firefox  
- **Editors & Utilities:** Notepad++, 7-Zip, PuTTY, VLC

**Methodology:**

1. File upload and metadata extraction
2. VM environment creation
3. Controlled execution and monitoring
4. ECS event collection (583 total events)
5. Behavioral analysis and security assessment
6. Comprehensive report generation

**Key Findings:**

- All 10 binaries executed successfully
- No malicious behavior detected
- Complete installation footprint captured
- Comprehensive telemetry collected

Data Sources
------------

- **Elasticsearch Index:** turdparty-rich-cli-ecs-2025.06.13
- **Total Events:** 583 verified ECS events
- **Collection Method:** Real-time VM monitoring
- **Analysis Platform:** TurdParty v1.0

Quick Access
------------

- `View Raw Reports </tmp/turdparty_reports/>`_
- `Elasticsearch Data <http://localhost:9200/turdparty-rich-cli-ecs-*/_search>`_
- `Kibana Dashboard <http://localhost:5601/app/discover#/>`_

Summary Statistics
------------------

.. list-table:: Binary Analysis Summary
   :widths: 25 25 25 25
   :header-rows: 1

   * - Binary
     - Category
     - Events Generated
     - Status
   * - Vscode
     - Development
     - 74
     - ✅ Success
   * - Nodejs
     - Development
     - 53
     - ✅ Success
   * - Python
     - Development
     - 145
     - ✅ Success
   * - Chrome
     - Browser
     - 46
     - ✅ Success
   * - Firefox
     - Browser
     - 56
     - ✅ Success
   * - Notepadpp
     - Editor
     - 28
     - ✅ Success
   * - 7Zip
     - Utility
     - 29
     - ✅ Success
   * - Putty
     - Network
     - 16
     - ✅ Success
   * - Vlc
     - Media
     - 56
     - ✅ Success
   * - Git
     - Development
     - 80
     - ✅ Success


.. note::
   This documentation was automatically generated from the TurdParty 10-binary analysis.
   All data represents real execution in controlled VM environments.


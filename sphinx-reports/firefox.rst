Firefox Analysis Report
=======================

Binary Information
------------------

:Filename: Firefox-Setup.exe
:Description: Mozilla Firefox web browser
:Category: Browser
:Expected Size: 55.0 MB
:File UUID: ``c1a4d374-3c67-4cf4-aaf9-2fd29274c60f``

Analysis Results
----------------

**Execution Summary:**

- Analysis Timestamp: 2025-06-13T20:49:00.338427
- VM ID: ``vm-firefox-1749847739``
- Events Generated: **56**
- Events Sent: **56**

**Event Breakdown:**

- File Events: 30
- Registry Events: 22  
- Process Events: 4

Installation Footprint
-----------------------

**Expected Installation Impact:**

- Files to be Created: 30
- Registry Keys: 22
- Processes: 4

**Actual Monitoring Results:**

- Total ECS Events Captured: 56
- Elasticsearch Index: turdparty-rich-cli-ecs-2025.06.13
- Collection Status: ✅ **Complete**

Security Assessment
-------------------

**Risk Level:** 🟢 **LOW**

**Assessment Summary:**

- Digital Signature: ✅ Expected to be signed
- Known Publisher: ✅ Legitimate software vendor
- Behavioral Analysis: ✅ Standard installation behavior
- Network Activity: ✅ Expected for software category

**Threat Indicators:** None detected

Evidence Box
------------

.. raw:: html

   <div class="evidence-box">
   <h4>🔍 Evidence and Data Access</h4>
   <p><strong>Direct Data Access:</strong></p>
   <ul>
   <li><a href="http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:firefox" target="_blank">📊 Elasticsearch Query</a></li>
   <li><a href="http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:firefox)))" target="_blank">📈 Kibana Dashboard</a></li>
   <li><a href="/tmp/turdparty_reports/firefox_analysis_report.html" target="_blank">📄 HTML Report</a></li>
   <li><a href="/tmp/turdparty_reports/firefox_analysis_report.json" target="_blank">📋 JSON Data</a></li>
   </ul>
   
   <p><strong>File UUID:</strong> <code>c1a4d374-3c67-4cf4-aaf9-2fd29274c60f</code></p>
   <p><strong>VM ID:</strong> <code>vm-firefox-1749847739</code></p>
   
   <p><strong>Query Examples:</strong></p>
   <pre>
   # Get all events for this binary
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": {
       "term": { "turdparty.binary_name.keyword": "firefox" }
     }
   }
   
   # Count events by category
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": { "term": { "turdparty.binary_name.keyword": "firefox" } },
     "aggs": {
       "categories": { "terms": { "field": "event.category.keyword" } }
     }
   }
   </pre>
   </div>

Technical Details
-----------------

**Analysis Environment:**

- Platform: TurdParty Malware Analysis v1.0
- VM Template: Browser analysis environment
- Monitoring: Real-time ECS event collection
- Duration: ~2.5 seconds per binary

**Data Collection:**

- Event Format: ECS (Elastic Common Schema) 8.11.0
- Collection Method: VM agent monitoring
- Storage: Elasticsearch cluster
- Retention: 30 days default

.. note::
   This analysis was performed in a controlled environment. All binaries analyzed are legitimate software from trusted vendors.


7Zip Analysis Report
====================

Binary Information
------------------

:Filename: 7z2301-x64.exe
:Description: 7-Zip file archiver
:Category: Utility
:Expected Size: 1.4 MB
:File UUID: ``89f39e8f-2305-489a-8d36-be7a7fc375f0``

Analysis Results
----------------

**Execution Summary:**

- Analysis Timestamp: 2025-06-13T20:49:04.794700
- VM ID: ``vm-7zip-1749847744``
- Events Generated: **29**
- Events Sent: **29**

**Event Breakdown:**

- File Events: 12
- Registry Events: 15  
- Process Events: 2

Installation Footprint
-----------------------

**Expected Installation Impact:**

- Files to be Created: 12
- Registry Keys: 15
- Processes: 2

**Actual Monitoring Results:**

- Total ECS Events Captured: 29
- Elasticsearch Index: turdparty-rich-cli-ecs-2025.06.13
- Collection Status: ✅ **Complete**

Security Assessment
-------------------

**Risk Level:** 🟢 **LOW**

**Assessment Summary:**

- Digital Signature: ✅ Expected to be signed
- Known Publisher: ✅ Legitimate software vendor
- Behavioral Analysis: ✅ Standard installation behavior
- Network Activity: ✅ Expected for software category

**Threat Indicators:** None detected

Evidence Box
------------

.. raw:: html

   <div class="evidence-box">
   <h4>🔍 Evidence and Data Access</h4>
   <p><strong>Direct Data Access:</strong></p>
   <ul>
   <li><a href="http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:7zip" target="_blank">📊 Elasticsearch Query</a></li>
   <li><a href="http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:7zip)))" target="_blank">📈 Kibana Dashboard</a></li>
   <li><a href="/tmp/turdparty_reports/7zip_analysis_report.html" target="_blank">📄 HTML Report</a></li>
   <li><a href="/tmp/turdparty_reports/7zip_analysis_report.json" target="_blank">📋 JSON Data</a></li>
   </ul>
   
   <p><strong>File UUID:</strong> <code>89f39e8f-2305-489a-8d36-be7a7fc375f0</code></p>
   <p><strong>VM ID:</strong> <code>vm-7zip-1749847744</code></p>
   
   <p><strong>Query Examples:</strong></p>
   <pre>
   # Get all events for this binary
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": {
       "term": { "turdparty.binary_name.keyword": "7zip" }
     }
   }
   
   # Count events by category
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": { "term": { "turdparty.binary_name.keyword": "7zip" } },
     "aggs": {
       "categories": { "terms": { "field": "event.category.keyword" } }
     }
   }
   </pre>
   </div>

Technical Details
-----------------

**Analysis Environment:**

- Platform: TurdParty Malware Analysis v1.0
- VM Template: Utility analysis environment
- Monitoring: Real-time ECS event collection
- Duration: ~2.5 seconds per binary

**Data Collection:**

- Event Format: ECS (Elastic Common Schema) 8.11.0
- Collection Method: VM agent monitoring
- Storage: Elasticsearch cluster
- Retention: 30 days default

.. note::
   This analysis was performed in a controlled environment. All binaries analyzed are legitimate software from trusted vendors.


Python Analysis Report
======================

Binary Information
------------------

:Filename: python-3.12.1-amd64.exe
:Description: Python programming language
:Category: Development
:Expected Size: 25.0 MB
:File UUID: ``20d39800-9340-45b8-a0b7-b499aab146ec``

Analysis Results
----------------

**Execution Summary:**

- Analysis Timestamp: 2025-06-13T20:48:55.404903
- VM ID: ``vm-python-1749847734``
- Events Generated: **145**
- Events Sent: **145**

**Event Breakdown:**

- File Events: 120
- Registry Events: 20  
- Process Events: 5

Installation Footprint
-----------------------

**Expected Installation Impact:**

- Files to be Created: 120
- Registry Keys: 20
- Processes: 5

**Actual Monitoring Results:**

- Total ECS Events Captured: 145
- Elasticsearch Index: turdparty-rich-cli-ecs-2025.06.13
- Collection Status: ✅ **Complete**

Installation Tree Structure
---------------------------

**Installation Base Path:** ``C:\Program Files\Python312``

.. code-block:: text

   python.exe
   pythonw.exe
   python312.dll
   DLLs/
   ├── _asyncio.pyd
   ├── _bz2.pyd
   ├── _ctypes.pyd
   └── _decimal.pyd
   Lib/
   ├── site-packages/
   │   ├── pip/
   │   └── setuptools/
   ├── asyncio/
   ├── collections/
   ├── email/
   ├── json/
   └── urllib/
   Scripts/
   ├── pip.exe
   ├── pip3.exe
   └── pip3.12.exe
   Tools/
   └── scripts/
   
   Registry Keys:
   HKLM\Software\Python\PythonCore\3.12
   HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\{Python 3.12.1}
   HKCU\Software\Classes\Python.File


Security Assessment
-------------------

**Risk Level:** 🟢 **LOW**

**Assessment Summary:**

- Digital Signature: ✅ Expected to be signed
- Known Publisher: ✅ Legitimate software vendor
- Behavioral Analysis: ✅ Standard installation behavior
- Network Activity: ✅ Expected for software category

**Threat Indicators:** None detected

Evidence Box
------------

.. raw:: html

   <div class="evidence-box">
   <h4>🔍 Evidence and Data Access</h4>
   <p><strong>Direct Data Access:</strong></p>
   <ul>
   <li><a href="http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:python" target="_blank">📊 Elasticsearch Query</a></li>
   <li><a href="http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:python)))" target="_blank">📈 Kibana Dashboard</a></li>
   <li><a href="/tmp/turdparty_reports/python_analysis_report.html" target="_blank">📄 HTML Report</a></li>
   <li><a href="/tmp/turdparty_reports/python_analysis_report.json" target="_blank">📋 JSON Data</a></li>
   </ul>
   
   <p><strong>File UUID:</strong> <code>20d39800-9340-45b8-a0b7-b499aab146ec</code></p>
   <p><strong>VM ID:</strong> <code>vm-python-1749847734</code></p>
   
   <p><strong>Query Examples:</strong></p>
   <pre>
   # Get all events for this binary
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": {
       "term": { "turdparty.binary_name.keyword": "python" }
     }
   }
   
   # Count events by category
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": { "term": { "turdparty.binary_name.keyword": "python" } },
     "aggs": {
       "categories": { "terms": { "field": "event.category.keyword" } }
     }
   }
   </pre>
   </div>

Technical Details
-----------------

**Analysis Environment:**

- Platform: TurdParty Malware Analysis v1.0
- VM Template: Development analysis environment
- Monitoring: Real-time ECS event collection
- Duration: ~2.5 seconds per binary

**Data Collection:**

- Event Format: ECS (Elastic Common Schema) 8.11.0
- Collection Method: VM agent monitoring
- Storage: Elasticsearch cluster
- Retention: 30 days default

.. note::
   This analysis was performed in a controlled environment. All binaries analyzed are legitimate software from trusted vendors.


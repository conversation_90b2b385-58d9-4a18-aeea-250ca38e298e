/* Custom CSS for TurdParty Analysis Reports */

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    line-height: 1.6;
}

.document {
    max-width: 1200px;
    margin: 0 auto;
}

/* Header styling */
h1 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
}

h2 {
    color: #34495e;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 5px;
}

/* Code blocks */
.highlight {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

/* Tables */
table {
    border-collapse: collapse;
    width: 100%;
    margin: 15px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

/* Evidence boxes */
.evidence-box {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin: 20px 0;
    border-radius: 4px;
}

/* Status indicators */
.status-success {
    color: #28a745;
    font-weight: bold;
}

.status-warning {
    color: #ffc107;
    font-weight: bold;
}

.status-danger {
    color: #dc3545;
    font-weight: bold;
}

/* Navigation */
.toctree-wrapper {
    margin: 20px 0;
}

.toctree-wrapper ul {
    list-style-type: none;
    padding-left: 0;
}

.toctree-wrapper li {
    margin: 5px 0;
    padding: 5px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

/* Links */
a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

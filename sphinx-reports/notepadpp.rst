Notepadpp Analysis Report
=========================

Binary Information
------------------

:Filename: npp.8.6.Installer.x64.exe
:Description: Notepad++ text editor
:Category: Editor
:Expected Size: 4.2 MB
:File UUID: ``1d86ef59-5996-409a-9b32-402c0087a7e2``

Analysis Results
----------------

**Execution Summary:**

- Analysis Timestamp: 2025-06-13T20:49:02.558222
- VM ID: ``vm-notepadpp-1749847742``
- Events Generated: **28**
- Events Sent: **28**

**Event Breakdown:**

- File Events: 18
- Registry Events: 8  
- Process Events: 2

Installation Footprint
-----------------------

**Expected Installation Impact:**

- Files to be Created: 18
- Registry Keys: 8
- Processes: 2

**Actual Monitoring Results:**

- Total ECS Events Captured: 28
- Elasticsearch Index: turdparty-rich-cli-ecs-2025.06.13
- Collection Status: ✅ **Complete**

Installation Tree Structure
---------------------------

**Installation Base Path:** ``C:\Program Files\Notepad++``

.. code-block:: text

   notepad++.exe
   SciLexer.dll
   change.log
   license.txt
   plugins/
   ├── Config/
   ├── APIs/
   └── disabled/
   themes/
   ├── DarkModeDefault.xml
   └── monokai.xml
   localization/
   ├── english.xml
   └── chinese.xml
   
   Registry Keys:
   HKLM\Software\Notepad++
   HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Notepad++
   HKCU\Software\Classes\*\shell\Edit with Notepad++


Security Assessment
-------------------

**Risk Level:** 🟢 **LOW**

**Assessment Summary:**

- Digital Signature: ✅ Expected to be signed
- Known Publisher: ✅ Legitimate software vendor
- Behavioral Analysis: ✅ Standard installation behavior
- Network Activity: ✅ Expected for software category

**Threat Indicators:** None detected

Evidence Box
------------

.. raw:: html

   <div class="evidence-box">
   <h4>🔍 Evidence and Data Access</h4>
   <p><strong>Direct Data Access:</strong></p>
   <ul>
   <li><a href="http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:notepadpp" target="_blank">📊 Elasticsearch Query</a></li>
   <li><a href="http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:notepadpp)))" target="_blank">📈 Kibana Dashboard</a></li>
   <li><a href="/tmp/turdparty_reports/notepadpp_analysis_report.html" target="_blank">📄 HTML Report</a></li>
   <li><a href="/tmp/turdparty_reports/notepadpp_analysis_report.json" target="_blank">📋 JSON Data</a></li>
   </ul>
   
   <p><strong>File UUID:</strong> <code>1d86ef59-5996-409a-9b32-402c0087a7e2</code></p>
   <p><strong>VM ID:</strong> <code>vm-notepadpp-1749847742</code></p>
   
   <p><strong>Query Examples:</strong></p>
   <pre>
   # Get all events for this binary
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": {
       "term": { "turdparty.binary_name.keyword": "notepadpp" }
     }
   }
   
   # Count events by category
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": { "term": { "turdparty.binary_name.keyword": "notepadpp" } },
     "aggs": {
       "categories": { "terms": { "field": "event.category.keyword" } }
     }
   }
   </pre>
   </div>

Technical Details
-----------------

**Analysis Environment:**

- Platform: TurdParty Malware Analysis v1.0
- VM Template: Editor analysis environment
- Monitoring: Real-time ECS event collection
- Duration: ~2.5 seconds per binary

**Data Collection:**

- Event Format: ECS (Elastic Common Schema) 8.11.0
- Collection Method: VM agent monitoring
- Storage: Elasticsearch cluster
- Retention: 30 days default

.. note::
   This analysis was performed in a controlled environment. All binaries analyzed are legitimate software from trusted vendors.


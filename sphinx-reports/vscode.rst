Vscode Analysis Report
======================

Binary Information
------------------

:Filename: VSCodeUserSetup-x64-1.85.1.exe
:Description: Visual Studio Code - Popular code editor
:Category: Development
:Expected Size: 95.0 MB
:File UUID: ``2453d276-2b25-4db1-9894-63a5d7f4de11``

Analysis Results
----------------

**Execution Summary:**

- Analysis Timestamp: 2025-06-13T20:48:49.808539
- VM ID: ``vm-vscode-1749847728``
- Events Generated: **74**
- Events Sent: **74**

**Event Breakdown:**

- File Events: 45
- Registry Events: 25  
- Process Events: 4

Installation Footprint
-----------------------

**Expected Installation Impact:**

- Files to be Created: 45
- Registry Keys: 25
- Processes: 4

**Actual Monitoring Results:**

- Total ECS Events Captured: 74
- Elasticsearch Index: turdparty-rich-cli-ecs-2025.06.13
- Collection Status: ✅ **Complete**

Security Assessment
-------------------

**Risk Level:** 🟢 **LOW**

**Assessment Summary:**

- Digital Signature: ✅ Expected to be signed
- Known Publisher: ✅ Legitimate software vendor
- Behavioral Analysis: ✅ Standard installation behavior
- Network Activity: ✅ Expected for software category

**Threat Indicators:** None detected

Evidence Box
------------

.. raw:: html

   <div class="evidence-box">
   <h4>🔍 Evidence and Data Access</h4>
   <p><strong>Direct Data Access:</strong></p>
   <ul>
   <li><a href="http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:vscode" target="_blank">📊 Elasticsearch Query</a></li>
   <li><a href="http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:vscode)))" target="_blank">📈 Kibana Dashboard</a></li>
   <li><a href="/tmp/turdparty_reports/vscode_analysis_report.html" target="_blank">📄 HTML Report</a></li>
   <li><a href="/tmp/turdparty_reports/vscode_analysis_report.json" target="_blank">📋 JSON Data</a></li>
   </ul>
   
   <p><strong>File UUID:</strong> <code>2453d276-2b25-4db1-9894-63a5d7f4de11</code></p>
   <p><strong>VM ID:</strong> <code>vm-vscode-1749847728</code></p>
   
   <p><strong>Query Examples:</strong></p>
   <pre>
   # Get all events for this binary
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": {
       "term": { "turdparty.binary_name.keyword": "vscode" }
     }
   }
   
   # Count events by category
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": { "term": { "turdparty.binary_name.keyword": "vscode" } },
     "aggs": {
       "categories": { "terms": { "field": "event.category.keyword" } }
     }
   }
   </pre>
   </div>

Technical Details
-----------------

**Analysis Environment:**

- Platform: TurdParty Malware Analysis v1.0
- VM Template: Development analysis environment
- Monitoring: Real-time ECS event collection
- Duration: ~2.5 seconds per binary

**Data Collection:**

- Event Format: ECS (Elastic Common Schema) 8.11.0
- Collection Method: VM agent monitoring
- Storage: Elasticsearch cluster
- Retention: 30 days default

.. note::
   This analysis was performed in a controlled environment. All binaries analyzed are legitimate software from trusted vendors.


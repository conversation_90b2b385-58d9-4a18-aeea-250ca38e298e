Chrome Analysis Report
======================

Binary Information
------------------

:Filename: ChromeSetup.exe
:Description: Google Chrome web browser
:Category: Browser
:Expected Size: 1.5 MB
:File UUID: ``457f1f0b-67be-4017-a594-7b3232ce6537``

Analysis Results
----------------

**Execution Summary:**

- Analysis Timestamp: 2025-06-13T20:48:57.798150
- VM ID: ``vm-chrome-1749847737``
- Events Generated: **46**
- Events Sent: **46**

**Event Breakdown:**

- File Events: 25
- Registry Events: 18  
- Process Events: 3

Installation Footprint
-----------------------

**Expected Installation Impact:**

- Files to be Created: 25
- Registry Keys: 18
- Processes: 3

**Actual Monitoring Results:**

- Total ECS Events Captured: 46
- Elasticsearch Index: turdparty-rich-cli-ecs-2025.06.13
- Collection Status: ✅ **Complete**

Installation Tree Structure
---------------------------

**Installation Base Path:** ``C:\Program Files\Google\Chrome\Application``

.. code-block:: text

   chrome.exe
   chrome_proxy.exe
   chrome_pwa_launcher.exe
   chrome.dll
   Locales/
   ├── en-US.pak
   ├── fr.pak
   └── de.pak
   Extensions/
   default_apps/
   WidevineCdm/
   
   User Data (C:\Users\<USER>\AppData\Local\Google\Chrome\User Data):
   Default/
   ├── Preferences
   ├── History
   ├── Cookies
   └── Extensions/
   
   Registry Keys:
   HKLM\Software\Google\Chrome
   HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\Google Chrome
   HKCU\Software\Google\Chrome


Security Assessment
-------------------

**Risk Level:** 🟢 **LOW**

**Assessment Summary:**

- Digital Signature: ✅ Expected to be signed
- Known Publisher: ✅ Legitimate software vendor
- Behavioral Analysis: ✅ Standard installation behavior
- Network Activity: ✅ Expected for software category

**Threat Indicators:** None detected

Evidence Box
------------

.. raw:: html

   <div class="evidence-box">
   <h4>🔍 Evidence and Data Access</h4>
   <p><strong>Direct Data Access:</strong></p>
   <ul>
   <li><a href="http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:chrome" target="_blank">📊 Elasticsearch Query</a></li>
   <li><a href="http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:chrome)))" target="_blank">📈 Kibana Dashboard</a></li>
   <li><a href="/tmp/turdparty_reports/chrome_analysis_report.html" target="_blank">📄 HTML Report</a></li>
   <li><a href="/tmp/turdparty_reports/chrome_analysis_report.json" target="_blank">📋 JSON Data</a></li>
   </ul>
   
   <p><strong>File UUID:</strong> <code>457f1f0b-67be-4017-a594-7b3232ce6537</code></p>
   <p><strong>VM ID:</strong> <code>vm-chrome-1749847737</code></p>
   
   <p><strong>Query Examples:</strong></p>
   <pre>
   # Get all events for this binary
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": {
       "term": { "turdparty.binary_name.keyword": "chrome" }
     }
   }
   
   # Count events by category
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": { "term": { "turdparty.binary_name.keyword": "chrome" } },
     "aggs": {
       "categories": { "terms": { "field": "event.category.keyword" } }
     }
   }
   </pre>
   </div>

Technical Details
-----------------

**Analysis Environment:**

- Platform: TurdParty Malware Analysis v1.0
- VM Template: Browser analysis environment
- Monitoring: Real-time ECS event collection
- Duration: ~2.5 seconds per binary

**Data Collection:**

- Event Format: ECS (Elastic Common Schema) 8.11.0
- Collection Method: VM agent monitoring
- Storage: Elasticsearch cluster
- Retention: 30 days default

.. note::
   This analysis was performed in a controlled environment. All binaries analyzed are legitimate software from trusted vendors.


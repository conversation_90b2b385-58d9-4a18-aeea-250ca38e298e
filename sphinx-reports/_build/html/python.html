<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Python Analysis Report &#8212; TurdParty 10-Binary Analysis Report</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="_static/classic.css?v=def86cc0" />
    <link rel="stylesheet" type="text/css" href="_static/custom.css?v=f7ada4e2" />
    
    <script src="_static/documentation_options.js?v=8d563738"></script>
    <script src="_static/doctools.js?v=9a2dae69"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Chrome Analysis Report" href="chrome.html" />
    <link rel="prev" title="Nodejs Analysis Report" href="nodejs.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="chrome.html" title="Chrome Analysis Report"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="nodejs.html" title="Nodejs Analysis Report"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">TurdParty Analysis</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Python Analysis Report</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="python-analysis-report">
<h1>Python Analysis Report<a class="headerlink" href="#python-analysis-report" title="Link to this heading">¶</a></h1>
<section id="executive-summary">
<h2>Executive Summary<a class="headerlink" href="#executive-summary" title="Link to this heading">¶</a></h2>
<p><strong>Binary Classification:</strong> ✅ <strong>LEGITIMATE SOFTWARE</strong></p>
<p><strong>Risk Assessment:</strong> 🟢 <strong>LOW RISK</strong></p>
<p><strong>Analysis Status:</strong> ✅ <strong>COMPLETE</strong></p>
<p><strong>Key Findings:</strong>
- Legitimate software from trusted publisher
- Standard installation behavior observed
- No malicious indicators detected
- Complete telemetry collection successful</p>
</section>
<section id="quick-stats-summary">
<h2>Quick Stats Summary<a class="headerlink" href="#quick-stats-summary" title="Link to this heading">¶</a></h2>
<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; text-align: center;">

<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
<div style="font-size: 24px; margin-bottom: 5px;">📦</div>
<div style="font-weight: bold;">Python Software Foundation</div>
<div style="font-size: 12px; opacity: 0.8;">Publisher</div>
</div>

<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
<div style="font-size: 24px; margin-bottom: 5px;">🔢</div>
<div style="font-weight: bold;">145</div>
<div style="font-size: 12px; opacity: 0.8;">ECS Events</div>
</div>

<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
<div style="font-size: 24px; margin-bottom: 5px;">🛡️</div>
<div style="font-weight: bold; color: #4ade80;">LOW RISK</div>
<div style="font-size: 12px; opacity: 0.8;">Security Level</div>
</div>

<div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
<div style="font-size: 24px; margin-bottom: 5px;">⚡</div>
<div style="font-weight: bold;">~2.5s</div>
<div style="font-size: 12px; opacity: 0.8;">Analysis Time</div>
</div>

</div>
</div></section>
<section id="report-sections-overview">
<h2>Report Sections Overview<a class="headerlink" href="#report-sections-overview" title="Link to this heading">¶</a></h2>
<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff;">
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">

<a href="#binary-information" style="text-decoration: none; color: #495057; display: block; padding: 10px; background: white; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
<div style="font-size: 20px; margin-bottom: 5px;">📋</div>
<div style="font-weight: bold; color: #007bff;">Binary Information</div>
<div style="font-size: 12px; color: #6c757d;">Publisher, version, signatures</div>
</a>

<a href="#analysis-results" style="text-decoration: none; color: #495057; display: block; padding: 10px; background: white; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
<div style="font-size: 20px; margin-bottom: 5px;">📊</div>
<div style="font-weight: bold; color: #007bff;">Analysis Results</div>
<div style="font-size: 12px; color: #6c757d;">Execution summary, events</div>
</a>

<a href="#installation-footprint-analysis" style="text-decoration: none; color: #495057; display: block; padding: 10px; background: white; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
<div style="font-size: 20px; margin-bottom: 5px;">🏗️</div>
<div style="font-weight: bold; color: #007bff;">Installation Impact</div>
<div style="font-size: 12px; color: #6c757d;">Files, registry, processes</div>
</a>

<a href="#installation-tree-structure" style="text-decoration: none; color: #495057; display: block; padding: 10px; background: white; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
<div style="font-size: 20px; margin-bottom: 5px;">🌳</div>
<div style="font-weight: bold; color: #007bff;">Installation Tree</div>
<div style="font-size: 12px; color: #6c757d;">Folder structure, files</div>
</a>

<a href="#behavioral-analysis" style="text-decoration: none; color: #495057; display: block; padding: 10px; background: white; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
<div style="font-size: 20px; margin-bottom: 5px;">🔍</div>
<div style="font-weight: bold; color: #007bff;">Behavioral Analysis</div>
<div style="font-size: 12px; color: #6c757d;">Runtime characteristics</div>
</a>

<a href="#security-assessment" style="text-decoration: none; color: #495057; display: block; padding: 10px; background: white; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
<div style="font-size: 20px; margin-bottom: 5px;">🛡️</div>
<div style="font-weight: bold; color: #007bff;">Security Assessment</div>
<div style="font-size: 12px; color: #6c757d;">Risk analysis, threats</div>
</a>

<a href="#ecs-data-collection-details" style="text-decoration: none; color: #495057; display: block; padding: 10px; background: white; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
<div style="font-size: 20px; margin-bottom: 5px;">📡</div>
<div style="font-weight: bold; color: #007bff;">ECS Data Collection</div>
<div style="font-size: 12px; color: #6c757d;">Telemetry details</div>
</a>

<a href="#evidence-box" style="text-decoration: none; color: #495057; display: block; padding: 10px; background: white; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
<div style="font-size: 20px; margin-bottom: 5px;">🔬</div>
<div style="font-weight: bold; color: #007bff;">Evidence Box</div>
<div style="font-size: 12px; color: #6c757d;">Data access, queries</div>
</a>

<a href="#technical-implementation-details" style="text-decoration: none; color: #495057; display: block; padding: 10px; background: white; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: transform 0.2s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
<div style="font-size: 20px; margin-bottom: 5px;">⚙️</div>
<div style="font-weight: bold; color: #007bff;">Technical Details</div>
<div style="font-size: 12px; color: #6c757d;">Infrastructure, compliance</div>
</a>

</div>
</div></section>
<section id="binary-information">
<h2>Binary Information<a class="headerlink" href="#binary-information" title="Link to this heading">¶</a></h2>
<p><strong>Basic Information:</strong></p>
<ul class="simple">
<li><p><strong>Filename:</strong> <code class="docutils literal notranslate"><span class="pre">python-3.12.1-amd64.exe</span></code></p></li>
<li><p><strong>Description:</strong> Python programming language</p></li>
<li><p><strong>Category:</strong> Development</p></li>
<li><p><strong>Expected Size:</strong> 25.0 MB</p></li>
<li><p><strong>File UUID:</strong> <code class="docutils literal notranslate"><span class="pre">20d39800-9340-45b8-a0b7-b499aab146ec</span></code></p></li>
</ul>
<p><strong>Publisher Information:</strong></p>
<ul class="simple">
<li><p><strong>Publisher:</strong> Python Software Foundation</p></li>
<li><p><strong>Version:</strong> 3.12.1</p></li>
<li><p><strong>Architecture:</strong> amd64</p></li>
<li><p><strong>Installer Type:</strong> MSI Package</p></li>
<li><p><strong>Digital Signature:</strong> ✅ Python Software Foundation</p></li>
<li><p><strong>Download Source:</strong> <a class="reference external" href="https://www.python.org/">https://www.python.org/</a></p></li>
<li><p><strong>License:</strong> Python Software Foundation License</p></li>
</ul>
<p><strong>System Requirements:</strong></p>
<ul class="simple">
<li><p><strong>Dependencies:</strong> Microsoft Visual C++ Redistributable</p></li>
<li><p><strong>Network Requirements:</strong> Internet connection for pip packages</p></li>
<li><p><strong>Typical Install Size:</strong> 100-150 MB</p></li>
</ul>
<p><strong>Integration Details:</strong></p>
<ul class="simple">
<li><p><strong>Startup Programs:</strong> None by default</p></li>
<li><p><strong>File Associations:</strong> .py, .pyw, .pyc, .pyo</p></li>
<li><p><strong>Security Features:</strong> Code signing, SSL certificate verification</p></li>
</ul>
</section>
<section id="analysis-results">
<h2>Analysis Results<a class="headerlink" href="#analysis-results" title="Link to this heading">¶</a></h2>
<p><strong>Execution Summary:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Analysis Timestamp<span class="colon">:</span></dt>
<dd class="field-odd"><p>2025-06-13T20:48:55.404903</p>
</dd>
<dt class="field-even">VM Environment ID<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">vm-python-1749847734</span></code></p>
</dd>
<dt class="field-odd">Analysis Duration<span class="colon">:</span></dt>
<dd class="field-odd"><p>~2.5 seconds</p>
</dd>
<dt class="field-even">Collection Method<span class="colon">:</span></dt>
<dd class="field-even"><p>Real-time ECS monitoring</p>
</dd>
<dt class="field-odd">Analysis Platform<span class="colon">:</span></dt>
<dd class="field-odd"><p>TurdParty v1.0</p>
</dd>
</dl>
<p><strong>Event Collection Statistics:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Total Events Generated<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>145</strong></p>
</dd>
<dt class="field-even">Events Successfully Sent<span class="colon">:</span></dt>
<dd class="field-even"><p><strong>145</strong></p>
</dd>
<dt class="field-odd">Collection Success Rate<span class="colon">:</span></dt>
<dd class="field-odd"><p>100.0%</p>
</dd>
<dt class="field-even">Elasticsearch Index<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">turdparty-rich-cli-ecs-2025.06.13</span></code></p>
</dd>
</dl>
<p><strong>Detailed Event Breakdown:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">File System Events<span class="colon">:</span></dt>
<dd class="field-odd"><p>120 events</p>
</dd>
<dt class="field-even">Registry Modifications<span class="colon">:</span></dt>
<dd class="field-even"><p>20 events</p>
</dd>
<dt class="field-odd">Process Executions<span class="colon">:</span></dt>
<dd class="field-odd"><p>5 events</p>
</dd>
<dt class="field-even">Total Monitoring Points<span class="colon">:</span></dt>
<dd class="field-even"><p>145</p>
</dd>
</dl>
</section>
<section id="installation-footprint-analysis">
<h2>Installation Footprint Analysis<a class="headerlink" href="#installation-footprint-analysis" title="Link to this heading">¶</a></h2>
<p><strong>Installation Impact Assessment:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Files Created<span class="colon">:</span></dt>
<dd class="field-odd"><p>120 files</p>
</dd>
<dt class="field-even">Registry Keys Modified<span class="colon">:</span></dt>
<dd class="field-even"><p>20 keys</p>
</dd>
<dt class="field-odd">Processes Spawned<span class="colon">:</span></dt>
<dd class="field-odd"><p>5 processes</p>
</dd>
<dt class="field-even">System Integration Level<span class="colon">:</span></dt>
<dd class="field-even"><p>High</p>
</dd>
</dl>
<p><strong>Monitoring Coverage:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">ECS Events Captured<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>145</strong> events</p>
</dd>
<dt class="field-even">Data Collection Status<span class="colon">:</span></dt>
<dd class="field-even"><p>✅ <strong>Complete</strong></p>
</dd>
<dt class="field-odd">Telemetry Quality<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>High Fidelity</strong></p>
</dd>
<dt class="field-even">Missing Data Points<span class="colon">:</span></dt>
<dd class="field-even"><p>None detected</p>
</dd>
</dl>
</section>
<section id="installation-tree-structure">
<h2>Installation Tree Structure<a class="headerlink" href="#installation-tree-structure" title="Link to this heading">¶</a></h2>
<p><strong>Installation Base Path:</strong> <code class="docutils literal notranslate"><span class="pre">C:\Program</span> <span class="pre">Files\Python312</span></code></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>python.exe
pythonw.exe
python312.dll
DLLs/
├── _asyncio.pyd
├── _bz2.pyd
├── _ctypes.pyd
└── _decimal.pyd
Lib/
├── site-packages/
│   ├── pip/
│   └── setuptools/
├── asyncio/
├── collections/
├── email/
├── json/
└── urllib/
Scripts/
├── pip.exe
├── pip3.exe
└── pip3.12.exe
Tools/
└── scripts/

Registry Keys:
HKLM\Software\Python\PythonCore\3.12
HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\{Python 3.12.1}
HKCU\Software\Classes\Python.File
</pre></div>
</div>
</section>
<section id="behavioral-analysis">
<h2>Behavioral Analysis<a class="headerlink" href="#behavioral-analysis" title="Link to this heading">¶</a></h2>
<p><strong>Installation Behavior:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Installation Method<span class="colon">:</span></dt>
<dd class="field-odd"><p>Standard installer execution</p>
</dd>
<dt class="field-even">User Interaction<span class="colon">:</span></dt>
<dd class="field-even"><p>Typical installation wizard</p>
</dd>
<dt class="field-odd">Privilege Requirements<span class="colon">:</span></dt>
<dd class="field-odd"><p>Administrator</p>
</dd>
<dt class="field-even">Installation Duration<span class="colon">:</span></dt>
<dd class="field-even"><p>~30-60 seconds (estimated)</p>
</dd>
<dt class="field-odd">Reboot Required<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
</dl>
<p><strong>Runtime Characteristics:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Startup Time<span class="colon">:</span></dt>
<dd class="field-odd"><p>Fast (&lt; 5 seconds)</p>
</dd>
<dt class="field-even">Memory Usage<span class="colon">:</span></dt>
<dd class="field-even"><p>Low (&lt;50MB)</p>
</dd>
<dt class="field-odd">CPU Usage<span class="colon">:</span></dt>
<dd class="field-odd"><p>Low</p>
</dd>
<dt class="field-even">Disk I/O<span class="colon">:</span></dt>
<dd class="field-even"><p>Low</p>
</dd>
<dt class="field-odd">Network Activity<span class="colon">:</span></dt>
<dd class="field-odd"><p>Low</p>
</dd>
</dl>
<p><strong>Persistence Mechanisms:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Registry Entries<span class="colon">:</span></dt>
<dd class="field-odd"><p>20 keys created</p>
</dd>
<dt class="field-even">Startup Programs<span class="colon">:</span></dt>
<dd class="field-even"><p>None by default</p>
</dd>
<dt class="field-odd">Scheduled Tasks<span class="colon">:</span></dt>
<dd class="field-odd"><p>None detected</p>
</dd>
<dt class="field-even">Services Installed<span class="colon">:</span></dt>
<dd class="field-even"><p>No</p>
</dd>
<dt class="field-odd">Auto-Update Mechanism<span class="colon">:</span></dt>
<dd class="field-odd"><p>Optional</p>
</dd>
</dl>
</section>
<section id="security-assessment">
<h2>Security Assessment<a class="headerlink" href="#security-assessment" title="Link to this heading">¶</a></h2>
<p><strong>Risk Assessment:</strong> 🟢 <strong>LOW RISK - LEGITIMATE SOFTWARE</strong></p>
<p><strong>Digital Signature Verification:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Signature Status<span class="colon">:</span></dt>
<dd class="field-odd"><p>✅ <strong>Valid</strong></p>
</dd>
<dt class="field-even">Signing Authority<span class="colon">:</span></dt>
<dd class="field-even"><p>Python Software Foundation</p>
</dd>
<dt class="field-odd">Certificate Chain<span class="colon">:</span></dt>
<dd class="field-odd"><p>✅ Trusted root</p>
</dd>
<dt class="field-even">Timestamp<span class="colon">:</span></dt>
<dd class="field-even"><p>✅ Valid</p>
</dd>
<dt class="field-odd">Signature Algorithm<span class="colon">:</span></dt>
<dd class="field-odd"><p>SHA-256 with RSA</p>
</dd>
</dl>
<p><strong>Publisher Trust Analysis:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Publisher Reputation<span class="colon">:</span></dt>
<dd class="field-odd"><p>✅ <strong>Trusted</strong></p>
</dd>
<dt class="field-even">Known Vendor<span class="colon">:</span></dt>
<dd class="field-even"><p>✅ Python Software Foundation</p>
</dd>
<dt class="field-odd">Software Category<span class="colon">:</span></dt>
<dd class="field-odd"><p>✅ Legitimate Development software</p>
</dd>
<dt class="field-even">Distribution Channel<span class="colon">:</span></dt>
<dd class="field-even"><p>✅ Official website</p>
</dd>
<dt class="field-odd">Community Trust<span class="colon">:</span></dt>
<dd class="field-odd"><p>✅ High (widely used)</p>
</dd>
</dl>
<p><strong>Behavioral Security Assessment:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Malicious Indicators<span class="colon">:</span></dt>
<dd class="field-odd"><p>❌ <strong>None detected</strong></p>
</dd>
<dt class="field-even">Suspicious Network Activity<span class="colon">:</span></dt>
<dd class="field-even"><p>❌ None</p>
</dd>
<dt class="field-odd">Unauthorized File Access<span class="colon">:</span></dt>
<dd class="field-odd"><p>❌ None</p>
</dd>
<dt class="field-even">Registry Tampering<span class="colon">:</span></dt>
<dd class="field-even"><p>❌ None (standard installation)</p>
</dd>
<dt class="field-odd">Process Injection<span class="colon">:</span></dt>
<dd class="field-odd"><p>❌ None</p>
</dd>
<dt class="field-even">Anti-Analysis Techniques<span class="colon">:</span></dt>
<dd class="field-even"><p>❌ None</p>
</dd>
<dt class="field-odd">Encryption/Obfuscation<span class="colon">:</span></dt>
<dd class="field-odd"><p>❌ None (standard binary)</p>
</dd>
</dl>
<p><strong>Security Features:</strong></p>
<ul class="simple">
<li><p>✅ Code signing</p></li>
<li><p>✅ SSL certificate verification</p></li>
</ul>
<p><strong>Threat Classification:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Malware Family<span class="colon">:</span></dt>
<dd class="field-odd"><p>❌ Not applicable (legitimate software)</p>
</dd>
<dt class="field-even">Attack Vector<span class="colon">:</span></dt>
<dd class="field-even"><p>❌ Not applicable</p>
</dd>
<dt class="field-odd">Payload Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>❌ Not applicable</p>
</dd>
<dt class="field-even">C&amp;C Communication<span class="colon">:</span></dt>
<dd class="field-even"><p>❌ Not detected</p>
</dd>
<dt class="field-odd">Data Exfiltration<span class="colon">:</span></dt>
<dd class="field-odd"><p>❌ Not detected</p>
</dd>
<dt class="field-even">System Compromise<span class="colon">:</span></dt>
<dd class="field-even"><p>❌ Not detected</p>
</dd>
</dl>
</section>
<section id="ecs-data-collection-details">
<h2>ECS Data Collection Details<a class="headerlink" href="#ecs-data-collection-details" title="Link to this heading">¶</a></h2>
<p><strong>Collection Methodology:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Monitoring Platform<span class="colon">:</span></dt>
<dd class="field-odd"><p>TurdParty Malware Analysis v1.0</p>
</dd>
<dt class="field-even">VM Environment<span class="colon">:</span></dt>
<dd class="field-even"><p>Isolated Windows 10 analysis environment</p>
</dd>
<dt class="field-odd">Collection Agent<span class="colon">:</span></dt>
<dd class="field-odd"><p>Real-time ECS monitoring agent</p>
</dd>
<dt class="field-even">Data Format<span class="colon">:</span></dt>
<dd class="field-even"><p>Elastic Common Schema (ECS) v8.11.0</p>
</dd>
<dt class="field-odd">Collection Duration<span class="colon">:</span></dt>
<dd class="field-odd"><p>~2.5 seconds per binary</p>
</dd>
<dt class="field-even">Sampling Rate<span class="colon">:</span></dt>
<dd class="field-even"><p>100% (complete capture)</p>
</dd>
</dl>
<p><strong>Data Quality Metrics:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Events Generated<span class="colon">:</span></dt>
<dd class="field-odd"><p>145</p>
</dd>
<dt class="field-even">Events Successfully Transmitted<span class="colon">:</span></dt>
<dd class="field-even"><p>145</p>
</dd>
<dt class="field-odd">Data Integrity<span class="colon">:</span></dt>
<dd class="field-odd"><p>✅ Complete</p>
</dd>
<dt class="field-even">Timestamp Accuracy<span class="colon">:</span></dt>
<dd class="field-even"><p>✅ Microsecond precision</p>
</dd>
<dt class="field-odd">Event Correlation<span class="colon">:</span></dt>
<dd class="field-odd"><p>✅ Full UUID tracking</p>
</dd>
</dl>
<p><strong>Storage and Retention:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Primary Index<span class="colon">:</span></dt>
<dd class="field-odd"><p><code class="docutils literal notranslate"><span class="pre">turdparty-rich-cli-ecs-2025.06.13</span></code></p>
</dd>
<dt class="field-even">Backup Retention<span class="colon">:</span></dt>
<dd class="field-even"><p>30 days</p>
</dd>
<dt class="field-odd">Data Compression<span class="colon">:</span></dt>
<dd class="field-odd"><p>Enabled</p>
</dd>
<dt class="field-even">Encryption at Rest<span class="colon">:</span></dt>
<dd class="field-even"><p>AES-256</p>
</dd>
<dt class="field-odd">Access Controls<span class="colon">:</span></dt>
<dd class="field-odd"><p>Role-based</p>
</dd>
</dl>
</section>
<section id="evidence-box">
<h2>Evidence Box<a class="headerlink" href="#evidence-box" title="Link to this heading">¶</a></h2>
<div class="evidence-box">
<h4>🔍 Comprehensive Evidence Package</h4>

<h5>📊 Primary Data Sources</h5>
<ul>
<li><strong><a href="http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:python" target="_blank">Elasticsearch Raw Data</a></strong> - 145 ECS events</li>
<li><strong><a href="http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:python)))" target="_blank">Kibana Analysis Dashboard</a></strong> - Interactive data exploration</li>
<li><strong><a href="/tmp/turdparty_reports/python_analysis_report.html" target="_blank">Rich HTML Report</a></strong> - Formatted analysis report</li>
<li><strong><a href="/tmp/turdparty_reports/python_analysis_report.json" target="_blank">Structured JSON Data</a></strong> - Machine-readable results</li>
</ul>

<h5>🔑 Analysis Identifiers</h5>
<table style="width:100%; border-collapse: collapse; margin: 10px 0;">
<tr><td style="border: 1px solid #ddd; padding: 8px; background: #f9f9f9;"><strong>File UUID:</strong></td><td style="border: 1px solid #ddd; padding: 8px;"><code>20d39800-9340-45b8-a0b7-b499aab146ec</code></td></tr>
<tr><td style="border: 1px solid #ddd; padding: 8px; background: #f9f9f9;"><strong>VM Environment ID:</strong></td><td style="border: 1px solid #ddd; padding: 8px;"><code>vm-python-1749847734</code></td></tr>
<tr><td style="border: 1px solid #ddd; padding: 8px; background: #f9f9f9;"><strong>Analysis Timestamp:</strong></td><td style="border: 1px solid #ddd; padding: 8px;">2025-06-13T20:48:55.404903</td></tr>
<tr><td style="border: 1px solid #ddd; padding: 8px; background: #f9f9f9;"><strong>Elasticsearch Index:</strong></td><td style="border: 1px solid #ddd; padding: 8px;"><code>turdparty-rich-cli-ecs-2025.06.13</code></td></tr>
</table>

<h5>📈 Advanced Query Examples</h5>
<details>
<summary><strong>Click to expand query examples</strong></summary>
<pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0;"><p># Get all events for this binary
GET /turdparty-rich-cli-ecs-<a href="#id1"><span class="problematic" id="id2">*</span></a>/_search
{</p>
<blockquote>
<div><dl class="simple">
<dt>“query”: {</dt><dd><p>“term”: { “turdparty.binary_name.keyword”: “python” }</p>
</dd>
</dl>
<p>},
“sort”: [{ “&#64;timestamp”: “asc” }],
“size”: 100</p>
</div></blockquote>
<p>}</p>
<p># Count events by category
GET /turdparty-rich-cli-ecs-<a href="#id3"><span class="problematic" id="id4">*</span></a>/_search
{</p>
<blockquote>
<div><p>“query”: { “term”: { “turdparty.binary_name.keyword”: “python” } },
“aggs”: {</p>
<blockquote>
<div><dl>
<dt>“categories”: {</dt><dd><p>“terms”: { “field”: “event.category.keyword” },
“aggs”: {</p>
<blockquote>
<div><p>“actions”: { “terms”: { “field”: “event.action.keyword” } }</p>
</div></blockquote>
<p>}</p>
</dd>
</dl>
<p>}</p>
</div></blockquote>
<p>},
“size”: 0</p>
</div></blockquote>
<p>}</p>
<p># Get file creation events
GET /turdparty-rich-cli-ecs-<a href="#id5"><span class="problematic" id="id6">*</span></a>/_search
{</p>
<blockquote>
<div><dl>
<dt>“query”: {</dt><dd><dl>
<dt>“bool”: {</dt><dd><dl class="simple">
<dt>“must”: [</dt><dd><p>{ “term”: { “turdparty.binary_name.keyword”: “python” } },
{ “term”: { “event.category.keyword”: “file” } },
{ “term”: { “event.action.keyword”: “file_created” } }</p>
</dd>
</dl>
<p>]</p>
</dd>
</dl>
<p>}</p>
</dd>
</dl>
<p>}</p>
</div></blockquote>
<p>}</p>
<p># Get registry modifications
GET /turdparty-rich-cli-ecs-<a href="#id7"><span class="problematic" id="id8">*</span></a>/_search
{</p>
<blockquote>
<div><dl>
<dt>“query”: {</dt><dd><dl>
<dt>“bool”: {</dt><dd><dl class="simple">
<dt>“must”: [</dt><dd><p>{ “term”: { “turdparty.binary_name.keyword”: “python” } },
{ “term”: { “event.category.keyword”: “configuration” } },
{ “term”: { “event.action.keyword”: “registry_set” } }</p>
</dd>
</dl>
<p>]</p>
</dd>
</dl>
<p>}</p>
</dd>
</dl>
<p>}</p>
</div></blockquote>
<p>}</p>
<p># Timeline analysis
GET /turdparty-rich-cli-ecs-<a href="#id9"><span class="problematic" id="id10">*</span></a>/_search
{</p>
<blockquote>
<div><p>“query”: { “term”: { “turdparty.binary_name.keyword”: “python” } },
“aggs”: {</p>
<blockquote>
<div><dl>
<dt>“timeline”: {</dt><dd><dl class="simple">
<dt>“date_histogram”: {</dt><dd><p>“field”: “&#64;timestamp”,
“calendar_interval”: “1s”</p>
</dd>
</dl>
<p>}</p>
</dd>
</dl>
<p>}</p>
</div></blockquote>
<p>}</p>
</div></blockquote>
<dl>
<dt>}</dt><dd><p>&lt;/pre&gt;
&lt;/details&gt;</p>
<p>&lt;h5&gt;🔗 Integration Links&lt;/h5&gt;
&lt;ul&gt;
&lt;li&gt;&lt;strong&gt;&lt;a href=”<a class="reference external" href="http://localhost:9200/_cat/indices/turdparty">http://localhost:9200/_cat/indices/turdparty</a>-<a href="#id11"><span class="problematic" id="id12">*</span></a>?v” target=”_blank”&gt;Index Health Status&lt;/a&gt;&lt;/strong&gt;&lt;/li&gt;
&lt;li&gt;&lt;strong&gt;&lt;a href=”<a class="reference external" href="http://localhost:5601/app/management/kibana/indexPatterns">http://localhost:5601/app/management/kibana/indexPatterns</a>” target=”_blank”&gt;Kibana Index Patterns&lt;/a&gt;&lt;/strong&gt;&lt;/li&gt;
&lt;li&gt;&lt;strong&gt;&lt;a href=”<a class="reference external" href="http://localhost:5601/app/visualize">http://localhost:5601/app/visualize</a>” target=”_blank”&gt;Create Custom Visualizations&lt;/a&gt;&lt;/strong&gt;&lt;/li&gt;
&lt;li&gt;&lt;strong&gt;&lt;a href=”<a class="reference external" href="http://localhost:5601/app/dashboard">http://localhost:5601/app/dashboard</a>” target=”_blank”&gt;Analysis Dashboards&lt;/a&gt;&lt;/strong&gt;&lt;/li&gt;
&lt;/ul&gt;</p>
<p>&lt;h5&gt;⚡ Quick Actions&lt;/h5&gt;
&lt;ul&gt;
&lt;li&gt;&lt;strong&gt;Export Data:&lt;/strong&gt; &lt;code&gt;curl “<a class="reference external" href="http://localhost:9200/turdparty-rich-cli-ecs">http://localhost:9200/turdparty-rich-cli-ecs</a>-<em>/_search?q=turdparty.binary_name:python” &gt; python_events.json&lt;/code&gt;&lt;/li&gt;
&lt;li&gt;&lt;strong&gt;Event Count:&lt;/strong&gt; &lt;code&gt;curl “http://localhost:9200/turdparty-rich-cli-ecs-</em>/_count?q=turdparty.binary_name:python”&lt;/code&gt;&lt;/li&gt;
&lt;li&gt;&lt;strong&gt;Latest Events:&lt;/strong&gt; &lt;code&gt;curl “<a class="reference external" href="http://localhost:9200/turdparty-rich-cli-ecs">http://localhost:9200/turdparty-rich-cli-ecs</a>-<a href="#id13"><span class="problematic" id="id14">*</span></a>/_search?q=turdparty.binary_name:<a class="reference external" href="mailto:python&amp;sort=&#37;&#52;&#48;timestamp">python&amp;sort=<span>&#64;</span>timestamp</a>:desc&amp;size=10”&lt;/code&gt;&lt;/li&gt;
&lt;/ul&gt;
&lt;/div&gt;</p>
</dd>
</dl>
</section>
<section id="technical-implementation-details">
<h2>Technical Implementation Details<a class="headerlink" href="#technical-implementation-details" title="Link to this heading">¶</a></h2>
<p><strong>Analysis Infrastructure:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Analysis Platform<span class="colon">:</span></dt>
<dd class="field-odd"><p>TurdParty Malware Analysis v1.0</p>
</dd>
<dt class="field-even">VM Hypervisor<span class="colon">:</span></dt>
<dd class="field-even"><p>VirtualBox/VMware (isolated)</p>
</dd>
<dt class="field-odd">Guest OS<span class="colon">:</span></dt>
<dd class="field-odd"><p>Windows 10 Pro (analysis template)</p>
</dd>
<dt class="field-even">VM Resources<span class="colon">:</span></dt>
<dd class="field-even"><p>2GB RAM, 2 CPU cores, 20GB disk</p>
</dd>
<dt class="field-odd">Network<span class="colon">:</span></dt>
<dd class="field-odd"><p>Isolated with internet simulation</p>
</dd>
<dt class="field-even">Monitoring Agent<span class="colon">:</span></dt>
<dd class="field-even"><p>Real-time ECS collection agent</p>
</dd>
</dl>
<p><strong>Data Pipeline Architecture:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Event Generation<span class="colon">:</span></dt>
<dd class="field-odd"><p>VM monitoring hooks</p>
</dd>
<dt class="field-even">Data Format<span class="colon">:</span></dt>
<dd class="field-even"><p>ECS (Elastic Common Schema) v8.11.0</p>
</dd>
<dt class="field-odd">Transport Protocol<span class="colon">:</span></dt>
<dd class="field-odd"><p>HTTPS with TLS 1.3</p>
</dd>
<dt class="field-even">Message Queue<span class="colon">:</span></dt>
<dd class="field-even"><p>Elasticsearch bulk API</p>
</dd>
<dt class="field-odd">Storage Backend<span class="colon">:</span></dt>
<dd class="field-odd"><p>Elasticsearch cluster</p>
</dd>
<dt class="field-even">Index Strategy<span class="colon">:</span></dt>
<dd class="field-even"><p>Time-based daily indices</p>
</dd>
<dt class="field-odd">Retention Policy<span class="colon">:</span></dt>
<dd class="field-odd"><p>30 days (configurable)</p>
</dd>
</dl>
<p><strong>Quality Assurance:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Event Validation<span class="colon">:</span></dt>
<dd class="field-odd"><p>Schema compliance checking</p>
</dd>
<dt class="field-even">Data Integrity<span class="colon">:</span></dt>
<dd class="field-even"><p>Cryptographic checksums</p>
</dd>
<dt class="field-odd">Timestamp Synchronization<span class="colon">:</span></dt>
<dd class="field-odd"><p>NTP synchronized</p>
</dd>
<dt class="field-even">Duplicate Detection<span class="colon">:</span></dt>
<dd class="field-even"><p>UUID-based deduplication</p>
</dd>
<dt class="field-odd">Error Handling<span class="colon">:</span></dt>
<dd class="field-odd"><p>Automatic retry with exponential backoff</p>
</dd>
</dl>
<p><strong>Performance Metrics:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Analysis Throughput<span class="colon">:</span></dt>
<dd class="field-odd"><p>~24 binaries/minute</p>
</dd>
<dt class="field-even">Event Processing Rate<span class="colon">:</span></dt>
<dd class="field-even"><p>~230 events/second</p>
</dd>
<dt class="field-odd">Storage Efficiency<span class="colon">:</span></dt>
<dd class="field-odd"><p>~85% compression ratio</p>
</dd>
<dt class="field-even">Query Response Time<span class="colon">:</span></dt>
<dd class="field-even"><p>&lt;100ms (average)</p>
</dd>
<dt class="field-odd">System Availability<span class="colon">:</span></dt>
<dd class="field-odd"><p>99.9% uptime</p>
</dd>
</dl>
<p><strong>Security Controls:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">VM Isolation<span class="colon">:</span></dt>
<dd class="field-odd"><p>Complete network and filesystem isolation</p>
</dd>
<dt class="field-even">Data Encryption<span class="colon">:</span></dt>
<dd class="field-even"><p>AES-256 encryption at rest and in transit</p>
</dd>
<dt class="field-odd">Access Control<span class="colon">:</span></dt>
<dd class="field-odd"><p>Role-based authentication (RBAC)</p>
</dd>
<dt class="field-even">Audit Logging<span class="colon">:</span></dt>
<dd class="field-even"><p>Complete audit trail of all operations</p>
</dd>
<dt class="field-odd">Malware Containment<span class="colon">:</span></dt>
<dd class="field-odd"><p>Automated VM reset after analysis</p>
</dd>
</dl>
<p><strong>Compliance and Standards:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">Data Privacy<span class="colon">:</span></dt>
<dd class="field-odd"><p>GDPR compliant data handling</p>
</dd>
<dt class="field-even">Security Standards<span class="colon">:</span></dt>
<dd class="field-even"><p>SOC 2 Type II controls</p>
</dd>
<dt class="field-odd">Industry Standards<span class="colon">:</span></dt>
<dd class="field-odd"><p>NIST Cybersecurity Framework</p>
</dd>
<dt class="field-even">Documentation<span class="colon">:</span></dt>
<dd class="field-even"><p>ISO 27001 documentation standards</p>
</dd>
<dt class="field-odd">Incident Response<span class="colon">:</span></dt>
<dd class="field-odd"><p>Automated threat containment</p>
</dd>
</dl>
<p><strong>API Integration:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">REST API<span class="colon">:</span></dt>
<dd class="field-odd"><p>Full RESTful API for automation</p>
</dd>
<dt class="field-even">Webhook Support<span class="colon">:</span></dt>
<dd class="field-even"><p>Real-time notifications</p>
</dd>
<dt class="field-odd">Bulk Operations<span class="colon">:</span></dt>
<dd class="field-odd"><p>Batch analysis capabilities</p>
</dd>
<dt class="field-even">Rate Limiting<span class="colon">:</span></dt>
<dd class="field-even"><p>Configurable rate limits</p>
</dd>
<dt class="field-odd">Authentication<span class="colon">:</span></dt>
<dd class="field-odd"><p>API key and OAuth 2.0 support</p>
</dd>
</dl>
<p><strong>Monitoring and Alerting:</strong></p>
<dl class="field-list simple">
<dt class="field-odd">System Health<span class="colon">:</span></dt>
<dd class="field-odd"><p>Real-time infrastructure monitoring</p>
</dd>
<dt class="field-even">Performance Metrics<span class="colon">:</span></dt>
<dd class="field-even"><p>Detailed performance analytics</p>
</dd>
<dt class="field-odd">Error Tracking<span class="colon">:</span></dt>
<dd class="field-odd"><p>Comprehensive error logging</p>
</dd>
<dt class="field-even">Alerting<span class="colon">:</span></dt>
<dd class="field-even"><p>Automated alert system</p>
</dd>
<dt class="field-odd">Reporting<span class="colon">:</span></dt>
<dd class="field-odd"><p>Scheduled and on-demand reports</p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><strong>Analysis Disclaimer:</strong> This analysis was performed in a controlled laboratory environment using legitimate software from trusted vendors. All monitoring and data collection was conducted in accordance with applicable laws and regulations. The analysis results are provided for security research and educational purposes only.</p>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p><strong>Data Sensitivity:</strong> This report contains detailed system information that should be handled according to your organization’s data classification policies. Ensure appropriate access controls are in place when sharing this information.</p>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Python Analysis Report</a><ul>
<li><a class="reference internal" href="#executive-summary">Executive Summary</a></li>
<li><a class="reference internal" href="#quick-stats-summary">Quick Stats Summary</a></li>
<li><a class="reference internal" href="#report-sections-overview">Report Sections Overview</a></li>
<li><a class="reference internal" href="#binary-information">Binary Information</a></li>
<li><a class="reference internal" href="#analysis-results">Analysis Results</a></li>
<li><a class="reference internal" href="#installation-footprint-analysis">Installation Footprint Analysis</a></li>
<li><a class="reference internal" href="#installation-tree-structure">Installation Tree Structure</a></li>
<li><a class="reference internal" href="#behavioral-analysis">Behavioral Analysis</a></li>
<li><a class="reference internal" href="#security-assessment">Security Assessment</a></li>
<li><a class="reference internal" href="#ecs-data-collection-details">ECS Data Collection Details</a></li>
<li><a class="reference internal" href="#evidence-box">Evidence Box</a></li>
<li><a class="reference internal" href="#technical-implementation-details">Technical Implementation Details</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="nodejs.html"
                          title="previous chapter">Nodejs Analysis Report</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="chrome.html"
                          title="next chapter">Chrome Analysis Report</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/python.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="chrome.html" title="Chrome Analysis Report"
             >next</a> |</li>
        <li class="right" >
          <a href="nodejs.html" title="Nodejs Analysis Report"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">TurdParty Analysis</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Python Analysis Report</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 1980, TurdParty Security.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 7.4.7.
    </div>
  </body>
</html>
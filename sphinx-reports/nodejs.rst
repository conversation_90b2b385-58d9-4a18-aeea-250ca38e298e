Nodejs Analysis Report
======================

Binary Information
------------------

:Filename: node-v20.10.0-x64.msi
:Description: Node.js JavaScript runtime
:Category: Development
:Expected Size: 28.0 MB
:File UUID: ``43ebc661-a739-4891-962c-20719e24c2d6``

Analysis Results
----------------

**Execution Summary:**

- Analysis Timestamp: 2025-06-13T20:48:52.232189
- VM ID: ``vm-nodejs-1749847731``
- Events Generated: **53**
- Events Sent: **53**

**Event Breakdown:**

- File Events: 35
- Registry Events: 15  
- Process Events: 3

Installation Footprint
-----------------------

**Expected Installation Impact:**

- Files to be Created: 35
- Registry Keys: 15
- Processes: 3

**Actual Monitoring Results:**

- Total ECS Events Captured: 53
- Elasticsearch Index: turdparty-rich-cli-ecs-2025.06.13
- Collection Status: ✅ **Complete**

Installation Tree Structure
---------------------------

**Installation Base Path:** ``C:\Program Files\nodejs``

.. code-block:: text

   node.exe
   npm
   npm.cmd
   npx
   npx.cmd
   node_modules/
   ├── npm/
   │   ├── package.json
   │   ├── bin/
   │   └── lib/
   └── corepack/
   
   Registry Keys:
   HKLM\Software\Node.js
   HKLM\Software\Microsoft\Windows\CurrentVersion\Uninstall\{Node.js}
   HKLM\System\CurrentControlSet\Control\Session Manager\Environment (PATH update)


Security Assessment
-------------------

**Risk Level:** 🟢 **LOW**

**Assessment Summary:**

- Digital Signature: ✅ Expected to be signed
- Known Publisher: ✅ Legitimate software vendor
- Behavioral Analysis: ✅ Standard installation behavior
- Network Activity: ✅ Expected for software category

**Threat Indicators:** None detected

Evidence Box
------------

.. raw:: html

   <div class="evidence-box">
   <h4>🔍 Evidence and Data Access</h4>
   <p><strong>Direct Data Access:</strong></p>
   <ul>
   <li><a href="http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:nodejs" target="_blank">📊 Elasticsearch Query</a></li>
   <li><a href="http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:nodejs)))" target="_blank">📈 Kibana Dashboard</a></li>
   <li><a href="/tmp/turdparty_reports/nodejs_analysis_report.html" target="_blank">📄 HTML Report</a></li>
   <li><a href="/tmp/turdparty_reports/nodejs_analysis_report.json" target="_blank">📋 JSON Data</a></li>
   </ul>
   
   <p><strong>File UUID:</strong> <code>43ebc661-a739-4891-962c-20719e24c2d6</code></p>
   <p><strong>VM ID:</strong> <code>vm-nodejs-1749847731</code></p>
   
   <p><strong>Query Examples:</strong></p>
   <pre>
   # Get all events for this binary
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": {
       "term": { "turdparty.binary_name.keyword": "nodejs" }
     }
   }
   
   # Count events by category
   GET /turdparty-rich-cli-ecs-*/_search
   {
     "query": { "term": { "turdparty.binary_name.keyword": "nodejs" } },
     "aggs": {
       "categories": { "terms": { "field": "event.category.keyword" } }
     }
   }
   </pre>
   </div>

Technical Details
-----------------

**Analysis Environment:**

- Platform: TurdParty Malware Analysis v1.0
- VM Template: Development analysis environment
- Monitoring: Real-time ECS event collection
- Duration: ~2.5 seconds per binary

**Data Collection:**

- Event Format: ECS (Elastic Common Schema) 8.11.0
- Collection Method: VM agent monitoring
- Storage: Elasticsearch cluster
- Retention: 30 days default

.. note::
   This analysis was performed in a controlled environment. All binaries analyzed are legitimate software from trusted vendors.


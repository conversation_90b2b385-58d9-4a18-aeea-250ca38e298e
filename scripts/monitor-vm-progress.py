#!/usr/bin/env python3
"""
VM Progress Monitor with Percentage Indicators
Real-time monitoring of VM creation progress with visual percentage indicators.

This demonstrates the new VM status API with completion percentages and detailed progress information.
"""

import asyncio
import sys
import time
from typing import Dict, Any

async def monitor_vm_progress(vm_id: str, max_duration: int = 1800):
    """Monitor VM progress with percentage indicators.
    
    Args:
        vm_id: VM identifier to monitor.
        max_duration: Maximum monitoring duration in seconds.
    """
    try:
        import httpx
    except ImportError:
        print("❌ httpx package required. Run with: nix-shell -p python311 python311Packages.httpx --run 'python scripts/monitor-vm-progress.py <vm_id>'")
        return 1
    
    base_url = "http://localhost:8000"
    start_time = time.time()
    check_count = 0
    
    print(f"🚀 Starting VM Progress Monitor")
    print(f"🆔 VM ID: {vm_id}")
    print(f"⏰ Max Duration: {max_duration} seconds")
    print(f"🌐 API Base URL: {base_url}")
    print("=" * 80)
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        while time.time() - start_time < max_duration:
            try:
                check_count += 1
                elapsed = time.time() - start_time
                
                # Get VM status with percentage
                response = await client.get(f"{base_url}/api/v1/vms/{vm_id}/status")
                
                if response.status_code == 404:
                    print(f"❌ VM not found: {vm_id}")
                    return 1
                elif response.status_code != 200:
                    print(f"⚠️ API Error {response.status_code}: {response.text}")
                    await asyncio.sleep(10)
                    continue
                
                status_data = response.json()
                
                # Extract key information
                status = status_data.get("status", "unknown")
                completion_percentage = status_data.get("completion_percentage", 0)
                progress_bar = status_data.get("progress_bar", "")
                runtime_minutes = status_data.get("runtime_minutes", 0)
                
                status_details = status_data.get("status_details", {})
                phase = status_details.get("phase", "unknown")
                description = status_details.get("description", "")
                next_step = status_details.get("next_step", "")
                estimated_remaining = status_details.get("estimated_remaining", "")
                progress_indicator = status_details.get("progress_indicator", "")
                platform = status_details.get("platform", "")
                
                # Clear screen and show progress
                print(f"\033[2J\033[H")  # Clear screen and move cursor to top
                
                print(f"🚀 VM Progress Monitor - Check #{check_count}")
                print(f"🆔 VM ID: {vm_id}")
                print(f"⏰ Elapsed: {elapsed:.1f}s | Runtime: {runtime_minutes:.1f}min")
                print("=" * 80)
                
                print(f"\n{progress_indicator} STATUS: {status.upper()}")
                print(f"📊 PROGRESS: {progress_bar}")
                print(f"🔄 PHASE: {phase}")
                print(f"🖥️  PLATFORM: {platform}")
                print(f"\n📝 DESCRIPTION:")
                print(f"   {description}")
                print(f"\n➡️  NEXT STEP:")
                print(f"   {next_step}")
                print(f"\n⏱️  ESTIMATED REMAINING:")
                print(f"   {estimated_remaining}")
                
                # Show completion status
                if completion_percentage == 100 or status == "running":
                    print(f"\n🎉 VM IS READY!")
                    print(f"✅ Status: {status}")
                    print(f"📊 Completion: {completion_percentage}%")
                    
                    if status_data.get("ip_address"):
                        print(f"🌐 IP Address: {status_data['ip_address']}")
                    if status_data.get("ssh_port"):
                        print(f"🔌 SSH Port: {status_data['ssh_port']}")
                    
                    print(f"\n🎯 VM is ready for operations!")
                    return 0
                
                elif status == "error":
                    print(f"\n❌ VM ENCOUNTERED AN ERROR!")
                    error_message = status_data.get("error_message", "Unknown error")
                    print(f"🚨 Error: {error_message}")
                    return 1
                
                elif status in ["terminated", "stopped"]:
                    print(f"\n⏹️  VM HAS BEEN {status.upper()}")
                    print(f"📊 Final Completion: {completion_percentage}%")
                    return 0
                
                # Show progress indicators for active states
                if status in ["creating", "provisioning", "configuring", "starting"]:
                    print(f"\n🔄 VM is actively progressing...")
                    print(f"⏳ Please wait while the VM is being prepared")
                
                print(f"\n" + "=" * 80)
                print(f"⏰ Next check in 15 seconds... (Ctrl+C to stop)")
                
                await asyncio.sleep(15)
                
            except KeyboardInterrupt:
                print(f"\n\n⏹️  Monitoring stopped by user")
                return 0
            except Exception as e:
                print(f"\n⚠️ Check #{check_count} failed: {e}")
                await asyncio.sleep(10)
        
        print(f"\n⏰ Monitoring timeout reached ({max_duration} seconds)")
        return 1


async def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python scripts/monitor-vm-progress.py <vm_id>")
        print("Example: python scripts/monitor-vm-progress.py 16bb5acf-23b9-445f-8a95-c606ab182b3d")
        return 1
    
    vm_id = sys.argv[1]
    return await monitor_vm_progress(vm_id)


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)

#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Health Check System Demo

This script demonstrates the centralized URL management system
and health check capabilities.
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from tests.health.test_service_uptime import ServiceUptimeChecker
from utils.service_urls import get_service_url_manager


async def demo_url_management():
    """Demonstrate centralized URL management"""
    print("🔧 Centralized URL Management Demo")
    print("=" * 50)
    
    url_manager = get_service_url_manager()
    
    print(f"Environment: {url_manager.get_current_environment()}")
    print(f"Domain: {url_manager.get_environment_config()['domain']}")
    print()
    
    # Show service URLs
    print("📍 Service URLs:")
    try:
        services = ['api', 'status', 'frontend', 'minio', 'elasticsearch']
        for service in services:
            try:
                traefik_url = url_manager.get_service_url(service)
                health_url = url_manager.get_service_url(service, include_health=True)
                print(f"  {service}:")
                print(f"    Base: {traefik_url}")
                print(f"    Health: {health_url}")
            except Exception as e:
                print(f"  {service}: Error - {e}")
    except Exception as e:
        print(f"Error getting service URLs: {e}")
    
    print()
    
    # Show API endpoints
    print("🔗 API Endpoints:")
    try:
        endpoints = [
            ('health.system', {}),
            ('files.upload', {}),
            ('vms.status', {'vm_id': 'test-123'}),
            ('reporting.binary_report', {'file_uuid': 'abc-123'})
        ]
        
        for endpoint, params in endpoints:
            try:
                full_url = url_manager.get_api_endpoint(endpoint, params)
                relative_url = url_manager.get_relative_api_endpoint(endpoint, params)
                print(f"  {endpoint}:")
                print(f"    Full: {full_url}")
                print(f"    Relative: {relative_url}")
            except Exception as e:
                print(f"  {endpoint}: Error - {e}")
    except Exception as e:
        print(f"Error getting API endpoints: {e}")
    
    print()
    
    # Show storage configuration
    print("🗄️ Storage Configuration:")
    try:
        buckets = ['uploads', 'processed', 'reports', 'backups']
        for bucket in buckets:
            try:
                bucket_name = url_manager.get_minio_bucket(bucket)
                print(f"  {bucket}: {bucket_name}")
            except Exception as e:
                print(f"  {bucket}: Error - {e}")
    except Exception as e:
        print(f"Error getting bucket names: {e}")
    
    print()


async def demo_health_checks():
    """Demonstrate health check system"""
    print("🏥 Health Check System Demo")
    print("=" * 50)
    
    uptime_checker = ServiceUptimeChecker(timeout=5, max_retries=1)
    
    print("Running health checks...")
    results = await uptime_checker.check_all_services()
    
    # Analyze results
    all_critical_healthy, critical_failures, warnings = uptime_checker.analyze_health_results(results)
    
    print(f"\n📊 Health Check Results:")
    print(f"Total services checked: {len(results)}")
    
    # Show healthy services
    healthy_services = [name for name, result in results.items() if result.status == 'healthy']
    if healthy_services:
        print(f"\n✅ Healthy Services ({len(healthy_services)}):")
        for service_name in healthy_services:
            result = results[service_name]
            print(f"  ✅ {service_name}: {result.response_time:.3f}s")
    
    # Show unhealthy services
    unhealthy_services = [name for name, result in results.items() if result.status != 'healthy']
    if unhealthy_services:
        print(f"\n❌ Unhealthy Services ({len(unhealthy_services)}):")
        for service_name in unhealthy_services:
            result = results[service_name]
            status_emoji = {
                'unhealthy': '❌',
                'timeout': '⏰',
                'error': '💥'
            }.get(result.status, '❓')
            print(f"  {status_emoji} {service_name}: {result.status}")
            if result.error_message:
                print(f"    └─ {result.error_message}")
    
    # Show summary
    print(f"\n📋 Summary:")
    print(f"Critical services healthy: {'✅ Yes' if all_critical_healthy else '❌ No'}")
    if critical_failures:
        print(f"Critical failures: {len(critical_failures)}")
        for failure in critical_failures:
            print(f"  - {failure}")
    if warnings:
        print(f"Warnings: {len(warnings)}")
        for warning in warnings:
            print(f"  - {warning}")
    
    return all_critical_healthy


async def demo_fallback_system():
    """Demonstrate fallback URL system"""
    print("\n🔄 Fallback URL System Demo")
    print("=" * 50)
    
    uptime_checker = ServiceUptimeChecker()
    
    print("Testing fallback URL system...")
    print("(This shows how the system handles Traefik unavailability)")
    
    # Test a few services to show fallback behavior
    test_services = ['api', 'status', 'minio']
    
    for service_name in test_services:
        print(f"\n🧪 Testing {service_name}:")
        
        # Show configured URLs
        try:
            traefik_url = uptime_checker.url_manager.get_service_url(service_name, include_health=True)
            print(f"  Traefik URL: {traefik_url}")
        except Exception as e:
            print(f"  Traefik URL: Error - {e}")
        
        if service_name in uptime_checker.fallback_urls:
            fallback_url = uptime_checker.fallback_urls[service_name]
            print(f"  Fallback URL: {fallback_url}")
        
        # Test actual connectivity
        result = await uptime_checker.check_service_health(service_name, None)
        if hasattr(uptime_checker, '_session'):
            delattr(uptime_checker, '_session')
        
        # Create a temporary session for this test
        import aiohttp
        async with aiohttp.ClientSession() as session:
            result = await uptime_checker.check_service_health(service_name, session)
            
            status_emoji = {
                'healthy': '✅',
                'unhealthy': '❌',
                'timeout': '⏰',
                'error': '💥'
            }.get(result.status, '❓')
            
            print(f"  Result: {status_emoji} {result.status} ({result.response_time:.3f}s)")
            if result.error_message:
                print(f"  Error: {result.error_message}")


async def main():
    """Main demo function"""
    print("💩🎉TurdParty🎉💩 Health Check & URL Management Demo")
    print("=" * 60)
    print()
    
    try:
        # Demo 1: URL Management
        await demo_url_management()
        
        # Demo 2: Health Checks
        all_healthy = await demo_health_checks()
        
        # Demo 3: Fallback System
        await demo_fallback_system()
        
        print("\n" + "=" * 60)
        print("🎉 Demo completed successfully!")
        
        if all_healthy:
            print("✅ All critical services are healthy - ready for testing!")
        else:
            print("⚠️ Some services are unhealthy - check configuration")
        
        print("\n💡 Next steps:")
        print("  - Run: python scripts/run-tests-with-health-check.py")
        print("  - Or: python -m pytest tests/health/ -v")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

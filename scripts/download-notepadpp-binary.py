#!/usr/bin/env python3
"""
Notepad++ Binary Downloader
Downloads the actual Notepad++ installer from GitHub releases for testing.

This script implements the binary download requirements from PRD-Notepadpp-API-Workflow-Testing.md
- Downloads actual Notepad++ installer from official GitHub releases
- Validates file size and integrity
- Computes Blake3 hash for verification
- Stores file with proper metadata
- No mocks, simulations, or emulation - only production code
"""

import asyncio
import hashlib
import logging
import os
import sys
import tempfile
import time
from pathlib import Path
from typing import Dict, Any, Optional

import httpx
import blake3

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NotepadPPBinaryDownloader:
    """Download and validate Notepad++ binary from official sources."""
    
    def __init__(self, download_dir: Optional[Path] = None):
        """Initialize the downloader.
        
        Args:
            download_dir: Directory to store downloaded files. Defaults to temp directory.
        """
        self.download_dir = download_dir or Path(tempfile.gettempdir()) / "turdparty-downloads"
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        # Notepad++ release information
        self.release_info = {
            "version": "8.5.8",
            "filename": "npp.8.5.8.Installer.x64.exe",
            "url": "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.Installer.x64.exe",
            "expected_size_min": 4_000_000,  # ~4MB minimum
            "expected_size_max": 6_000_000,  # ~6MB maximum
            "description": "Notepad++ Text Editor v8.5.8 x64 Installer"
        }
        
        self.session = httpx.AsyncClient(
            timeout=httpx.Timeout(300.0),  # 5 minute timeout
            follow_redirects=True,
            headers={
                "User-Agent": "TurdParty-Binary-Downloader/1.0"
            }
        )
    
    async def download_binary(self) -> Dict[str, Any]:
        """Download the Notepad++ binary.
        
        Returns:
            Dictionary containing download results and metadata.
        """
        logger.info("🚀 Starting Notepad++ binary download")
        logger.info(f"📁 Download directory: {self.download_dir}")
        
        file_path = self.download_dir / self.release_info["filename"]
        
        try:
            # Check if file already exists
            if file_path.exists():
                logger.info(f"📄 File already exists: {file_path}")
                return await self._validate_existing_file(file_path)
            
            # Download the file
            logger.info(f"📥 Downloading from: {self.release_info['url']}")
            start_time = time.time()
            
            async with self.session.stream("GET", self.release_info["url"]) as response:
                response.raise_for_status()
                
                # Get content length for progress tracking
                content_length = response.headers.get("content-length")
                if content_length:
                    total_size = int(content_length)
                    logger.info(f"📊 Expected file size: {total_size:,} bytes")
                
                # Download with progress tracking
                downloaded_size = 0
                with open(file_path, "wb") as f:
                    async for chunk in response.aiter_bytes(chunk_size=8192):
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # Log progress every 1MB
                        if downloaded_size % (1024 * 1024) == 0:
                            logger.info(f"📈 Downloaded: {downloaded_size:,} bytes")
            
            download_time = time.time() - start_time
            logger.info(f"✅ Download completed in {download_time:.2f} seconds")
            
            # Validate the downloaded file
            return await self._validate_downloaded_file(file_path, download_time)
            
        except Exception as e:
            logger.error(f"❌ Download failed: {e}")
            # Clean up partial download
            if file_path.exists():
                file_path.unlink()
            raise
    
    async def _validate_existing_file(self, file_path: Path) -> Dict[str, Any]:
        """Validate an existing file.
        
        Args:
            file_path: Path to the existing file.
            
        Returns:
            Validation results.
        """
        logger.info("🔍 Validating existing file")
        
        file_size = file_path.stat().st_size
        logger.info(f"📊 File size: {file_size:,} bytes")
        
        # Validate file size
        if not self._validate_file_size(file_size):
            logger.error("❌ File size validation failed")
            file_path.unlink()  # Remove invalid file
            return await self.download_binary()  # Re-download
        
        # Compute hashes
        hashes = await self._compute_file_hashes(file_path)
        
        return {
            "success": True,
            "file_path": str(file_path),
            "file_size": file_size,
            "download_time": 0.0,  # Existing file
            "blake3_hash": hashes["blake3"],
            "sha256_hash": hashes["sha256"],
            "metadata": self.release_info,
            "validation": {
                "size_valid": True,
                "hashes_computed": True
            }
        }
    
    async def _validate_downloaded_file(self, file_path: Path, download_time: float) -> Dict[str, Any]:
        """Validate a newly downloaded file.
        
        Args:
            file_path: Path to the downloaded file.
            download_time: Time taken to download.
            
        Returns:
            Validation results.
        """
        logger.info("🔍 Validating downloaded file")
        
        file_size = file_path.stat().st_size
        logger.info(f"📊 Final file size: {file_size:,} bytes")
        
        # Validate file size
        size_valid = self._validate_file_size(file_size)
        if not size_valid:
            logger.error("❌ File size validation failed")
            file_path.unlink()
            raise ValueError(f"Invalid file size: {file_size}")
        
        # Compute hashes
        logger.info("🔐 Computing file hashes...")
        hashes = await self._compute_file_hashes(file_path)
        
        logger.info(f"✅ Blake3 hash: {hashes['blake3']}")
        logger.info(f"✅ SHA256 hash: {hashes['sha256']}")
        
        return {
            "success": True,
            "file_path": str(file_path),
            "file_size": file_size,
            "download_time": download_time,
            "blake3_hash": hashes["blake3"],
            "sha256_hash": hashes["sha256"],
            "metadata": self.release_info,
            "validation": {
                "size_valid": size_valid,
                "hashes_computed": True
            }
        }
    
    def _validate_file_size(self, file_size: int) -> bool:
        """Validate file size is within expected range.
        
        Args:
            file_size: Size of the file in bytes.
            
        Returns:
            True if size is valid, False otherwise.
        """
        min_size = self.release_info["expected_size_min"]
        max_size = self.release_info["expected_size_max"]
        
        if min_size <= file_size <= max_size:
            logger.info(f"✅ File size validation passed: {file_size:,} bytes")
            return True
        else:
            logger.error(f"❌ File size out of range: {file_size:,} bytes (expected {min_size:,} - {max_size:,})")
            return False
    
    async def _compute_file_hashes(self, file_path: Path) -> Dict[str, str]:
        """Compute Blake3 and SHA256 hashes of the file.
        
        Args:
            file_path: Path to the file.
            
        Returns:
            Dictionary containing computed hashes.
        """
        blake3_hasher = blake3.blake3()
        sha256_hasher = hashlib.sha256()
        
        with open(file_path, "rb") as f:
            while chunk := f.read(8192):
                blake3_hasher.update(chunk)
                sha256_hasher.update(chunk)
        
        return {
            "blake3": blake3_hasher.hexdigest(),
            "sha256": sha256_hasher.hexdigest()
        }
    
    async def cleanup(self):
        """Clean up resources."""
        await self.session.aclose()


async def main():
    """Main function to download Notepad++ binary."""
    logger.info("🎯 Notepad++ Binary Downloader - Production Implementation")
    logger.info("📋 PRD: PRD-Notepadpp-API-Workflow-Testing.md")
    logger.info("🚫 No mocks, simulations, or emulation - production code only")
    
    downloader = NotepadPPBinaryDownloader()
    
    try:
        result = await downloader.download_binary()
        
        logger.info("\n📋 DOWNLOAD SUMMARY:")
        logger.info(f"✅ Success: {result['success']}")
        logger.info(f"📄 File: {result['file_path']}")
        logger.info(f"📊 Size: {result['file_size']:,} bytes")
        logger.info(f"⏱️  Time: {result['download_time']:.2f} seconds")
        logger.info(f"🔐 Blake3: {result['blake3_hash']}")
        logger.info(f"🔐 SHA256: {result['sha256_hash']}")
        
        logger.info("\n🎉 Binary download completed successfully!")
        logger.info("📝 Ready for MinIO upload and API workflow testing")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Download failed: {e}")
        return None
    finally:
        await downloader.cleanup()


if __name__ == "__main__":
    asyncio.run(main())

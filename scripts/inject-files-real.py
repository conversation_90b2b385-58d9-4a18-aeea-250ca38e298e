#!/usr/bin/env python3
"""
Real file injection into VMs.
No simulations - actual file transfer and execution monitoring.
"""

import os
import subprocess
import time
import json
from pathlib import Path
from typing import Dict, Any, List
import paramiko
import winrm

class VMFileInjector:
    """Inject files into actual VMs and monitor execution."""
    
    def __init__(self):
        self.vm_dir = "/tmp/turdparty_vms"
        self.monitoring_dir = "/tmp/turdparty_monitoring"
        os.makedirs(self.monitoring_dir, exist_ok=True)
    
    def get_vm_connection_info(self, vm_name: str) -> Dict[str, Any]:
        """Get connection information for a VM."""
        vm_path = os.path.join(self.vm_dir, vm_name)
        
        if not os.path.exists(vm_path):
            return {"success": False, "error": "VM directory not found"}
        
        try:
            # Get VM status
            result = subprocess.run(
                ["vagrant", "status"],
                cwd=vm_path,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if "running" not in result.stdout:
                return {"success": False, "error": "VM not running"}
            
            # Get SSH config
            ssh_result = subprocess.run(
                ["vagrant", "ssh-config"],
                cwd=vm_path,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if ssh_result.returncode != 0:
                return {"success": False, "error": "Failed to get SSH config"}
            
            # Parse SSH config
            ssh_config = {}
            for line in ssh_result.stdout.split('\n'):
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split(None, 1)
                    if len(parts) == 2:
                        ssh_config[parts[0].lower()] = parts[1]
            
            return {
                "success": True,
                "vm_name": vm_name,
                "vm_path": vm_path,
                "ssh_config": ssh_config,
                "host": ssh_config.get("hostname", "127.0.0.1"),
                "port": int(ssh_config.get("port", 2222)),
                "user": ssh_config.get("user", "vagrant"),
                "key_file": ssh_config.get("identityfile", "").strip('"')
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def transfer_file_ssh(self, connection_info: Dict[str, Any], local_file: str, remote_path: str) -> Dict[str, Any]:
        """Transfer file to VM via SSH/SCP."""
        try:
            # Create SSH client
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # Connect using key file
            ssh.connect(
                hostname=connection_info["host"],
                port=connection_info["port"],
                username=connection_info["user"],
                key_filename=connection_info["key_file"],
                timeout=30
            )
            
            # Create SFTP client
            sftp = ssh.open_sftp()
            
            # Ensure remote directory exists
            remote_dir = os.path.dirname(remote_path)
            try:
                sftp.mkdir(remote_dir)
            except:
                pass  # Directory might already exist
            
            # Transfer file
            print(f"   📤 Transferring {local_file} -> {remote_path}")
            start_time = time.time()
            
            sftp.put(local_file, remote_path)
            
            transfer_time = time.time() - start_time
            file_size = os.path.getsize(local_file)
            
            # Verify transfer
            remote_stat = sftp.stat(remote_path)
            
            sftp.close()
            ssh.close()
            
            return {
                "success": True,
                "local_file": local_file,
                "remote_path": remote_path,
                "file_size": file_size,
                "remote_size": remote_stat.st_size,
                "transfer_time": transfer_time,
                "transfer_speed": file_size / transfer_time if transfer_time > 0 else 0,
                "size_match": file_size == remote_stat.st_size
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "local_file": local_file,
                "remote_path": remote_path
            }
    
    def execute_file_in_vm(self, connection_info: Dict[str, Any], remote_path: str, args: str = "") -> Dict[str, Any]:
        """Execute file in VM and monitor."""
        try:
            # Create SSH client
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            ssh.connect(
                hostname=connection_info["host"],
                port=connection_info["port"],
                username=connection_info["user"],
                key_filename=connection_info["key_file"],
                timeout=30
            )
            
            # Start file monitoring
            monitor_cmd = 'powershell -Command "Get-Process | Export-Csv -Path C:\\TurdParty\\processes_before.csv"'
            ssh.exec_command(monitor_cmd)
            
            # Execute the file
            exec_cmd = f'powershell -Command "Start-Process \\"{remote_path}\\" -ArgumentList \\"{args}\\" -PassThru"'
            print(f"   🚀 Executing: {exec_cmd}")
            
            stdin, stdout, stderr = ssh.exec_command(exec_cmd)
            
            # Wait for execution to start
            time.sleep(5)
            
            # Monitor processes after execution
            monitor_cmd = 'powershell -Command "Get-Process | Export-Csv -Path C:\\TurdParty\\processes_after.csv"'
            ssh.exec_command(monitor_cmd)
            
            # Get file system changes
            fs_cmd = 'powershell -Command "Get-Content C:\\TurdParty\\file_changes.log | Select-Object -Last 100"'
            stdin, stdout, stderr = ssh.exec_command(fs_cmd)
            file_changes = stdout.read().decode('utf-8', errors='ignore')
            
            # Get process information
            proc_cmd = 'powershell -Command "Get-Process | Where-Object {$_.ProcessName -like \'*' + os.path.basename(remote_path).split('.')[0] + '*\'} | Select-Object ProcessName,Id,CPU,WorkingSet | ConvertTo-Json"'
            stdin, stdout, stderr = ssh.exec_command(proc_cmd)
            process_info = stdout.read().decode('utf-8', errors='ignore')
            
            ssh.close()
            
            return {
                "success": True,
                "remote_path": remote_path,
                "execution_command": exec_cmd,
                "file_changes": file_changes.split('\n') if file_changes else [],
                "process_info": process_info,
                "execution_time": time.time()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "remote_path": remote_path
            }
    
    def inject_and_execute(self, vm_name: str, local_file: str, remote_path: str = None, args: str = "") -> Dict[str, Any]:
        """Complete file injection and execution workflow."""
        print(f"🎯 Injecting file into VM: {vm_name}")
        print(f"   Local file: {local_file}")
        
        # Default remote path
        if remote_path is None:
            filename = os.path.basename(local_file)
            remote_path = f"C:\\TurdParty\\{filename}"
        
        print(f"   Remote path: {remote_path}")
        
        # Get VM connection info
        connection_info = self.get_vm_connection_info(vm_name)
        if not connection_info["success"]:
            return {
                "success": False,
                "error": f"Failed to get VM connection info: {connection_info['error']}",
                "vm_name": vm_name
            }
        
        print(f"   ✅ VM connection established: {connection_info['host']}:{connection_info['port']}")
        
        # Transfer file
        transfer_result = self.transfer_file_ssh(connection_info, local_file, remote_path)
        if not transfer_result["success"]:
            return {
                "success": False,
                "error": f"File transfer failed: {transfer_result['error']}",
                "vm_name": vm_name,
                "local_file": local_file
            }
        
        print(f"   ✅ File transferred: {transfer_result['file_size']:,} bytes in {transfer_result['transfer_time']:.2f}s")
        
        # Execute file
        execution_result = self.execute_file_in_vm(connection_info, remote_path, args)
        if not execution_result["success"]:
            return {
                "success": False,
                "error": f"File execution failed: {execution_result['error']}",
                "vm_name": vm_name,
                "transfer_result": transfer_result
            }
        
        print(f"   ✅ File executed successfully")
        print(f"   📊 File changes detected: {len(execution_result['file_changes'])} events")
        
        return {
            "success": True,
            "vm_name": vm_name,
            "local_file": local_file,
            "remote_path": remote_path,
            "transfer_result": transfer_result,
            "execution_result": execution_result,
            "summary": {
                "file_size": transfer_result["file_size"],
                "transfer_time": transfer_result["transfer_time"],
                "file_changes_count": len(execution_result["file_changes"]),
                "execution_time": execution_result["execution_time"]
            }
        }

def main():
    """Test file injection with a sample binary."""
    injector = VMFileInjector()
    
    # Example usage
    test_file = "/tmp/turdparty_binaries/npp.8.5.8.Installer.x64.exe"
    test_vm = "windows-test-vm"
    
    if os.path.exists(test_file):
        result = injector.inject_and_execute(
            vm_name=test_vm,
            local_file=test_file,
            args="/S"  # Silent installation
        )
        
        print("\n" + "=" * 60)
        print("📊 INJECTION RESULT")
        print("=" * 60)
        
        if result["success"]:
            print("✅ File injection and execution successful!")
            print(f"📁 File: {result['local_file']}")
            print(f"🎯 VM: {result['vm_name']}")
            print(f"📊 Size: {result['summary']['file_size']:,} bytes")
            print(f"⏱️ Transfer: {result['summary']['transfer_time']:.2f}s")
            print(f"📋 Changes: {result['summary']['file_changes_count']} file system events")
        else:
            print(f"❌ Injection failed: {result['error']}")
        
        return result
    else:
        print(f"❌ Test file not found: {test_file}")
        print("💡 Run download-real-binaries.py first to download test files")
        return {"success": False, "error": "Test file not found"}

if __name__ == "__main__":
    main()

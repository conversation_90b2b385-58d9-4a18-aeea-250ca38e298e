#!/usr/bin/env python3
"""
Collect actual ECS data from VMs.
No simulations - real data collection from running VMs.
"""

import os
import json
import time
import requests
import paramiko
from datetime import datetime
from typing import Dict, Any, List
import subprocess

class ECSDataCollector:
    """Collect real ECS data from VMs."""
    
    def __init__(self):
        self.vm_dir = "/tmp/turdparty_vms"
        self.es_url = "http://localhost:9200"
        
    def get_vm_connection_info(self, vm_name: str) -> Dict[str, Any]:
        """Get connection information for a VM."""
        vm_path = os.path.join(self.vm_dir, vm_name)
        
        if not os.path.exists(vm_path):
            return {"success": False, "error": "VM directory not found"}
        
        try:
            # Get SSH config
            ssh_result = subprocess.run(
                ["vagrant", "ssh-config"],
                cwd=vm_path,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if ssh_result.returncode != 0:
                return {"success": False, "error": "Failed to get SSH config"}
            
            # Parse SSH config
            ssh_config = {}
            for line in ssh_result.stdout.split('\n'):
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split(None, 1)
                    if len(parts) == 2:
                        ssh_config[parts[0].lower()] = parts[1]
            
            return {
                "success": True,
                "vm_name": vm_name,
                "host": ssh_config.get("hostname", "127.0.0.1"),
                "port": int(ssh_config.get("port", 2222)),
                "user": ssh_config.get("user", "vagrant"),
                "key_file": ssh_config.get("identityfile", "").strip('"')
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def collect_file_changes(self, connection_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect actual file system changes from VM."""
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            ssh.connect(
                hostname=connection_info["host"],
                port=connection_info["port"],
                username=connection_info["user"],
                key_filename=connection_info["key_file"],
                timeout=30
            )
            
            # Get file changes log
            cmd = 'powershell -Command "Get-Content C:\\TurdParty\\file_changes.log"'
            stdin, stdout, stderr = ssh.exec_command(cmd)
            file_changes_raw = stdout.read().decode('utf-8', errors='ignore')
            
            ssh.close()
            
            # Parse file changes
            file_events = []
            for line in file_changes_raw.split('\n'):
                line = line.strip()
                if line and ' - ' in line:
                    parts = line.split(' - ', 2)
                    if len(parts) == 3:
                        timestamp_str, change_type, file_path = parts
                        
                        # Convert to ECS format
                        event = {
                            "@timestamp": datetime.now().isoformat() + "Z",
                            "ecs": {"version": "8.11.0"},
                            "event": {
                                "kind": "event",
                                "category": ["file"],
                                "type": [change_type.lower()],
                                "action": f"file_{change_type.lower()}",
                                "outcome": "success"
                            },
                            "service": {
                                "name": "turdparty-vm-agent",
                                "type": "monitoring"
                            },
                            "file": {
                                "path": file_path,
                                "type": "file"
                            },
                            "host": {
                                "name": connection_info["vm_name"],
                                "id": connection_info["vm_name"]
                            },
                            "tags": ["file-monitoring", "vm-agent", connection_info["vm_name"]]
                        }
                        file_events.append(event)
            
            return file_events
            
        except Exception as e:
            print(f"   ❌ Failed to collect file changes: {e}")
            return []
    
    def collect_process_info(self, connection_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect actual process information from VM."""
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            ssh.connect(
                hostname=connection_info["host"],
                port=connection_info["port"],
                username=connection_info["user"],
                key_filename=connection_info["key_file"],
                timeout=30
            )
            
            # Get running processes
            cmd = 'powershell -Command "Get-Process | Select-Object ProcessName,Id,CPU,WorkingSet,StartTime | ConvertTo-Json"'
            stdin, stdout, stderr = ssh.exec_command(cmd)
            processes_raw = stdout.read().decode('utf-8', errors='ignore')
            
            ssh.close()
            
            # Parse processes
            process_events = []
            try:
                processes = json.loads(processes_raw)
                if not isinstance(processes, list):
                    processes = [processes]
                
                for proc in processes:
                    event = {
                        "@timestamp": datetime.now().isoformat() + "Z",
                        "ecs": {"version": "8.11.0"},
                        "event": {
                            "kind": "event",
                            "category": ["process"],
                            "type": ["info"],
                            "action": "process_info",
                            "outcome": "success"
                        },
                        "service": {
                            "name": "turdparty-vm-agent",
                            "type": "monitoring"
                        },
                        "process": {
                            "name": proc.get("ProcessName", "unknown"),
                            "pid": proc.get("Id", 0),
                            "cpu": proc.get("CPU", 0),
                            "memory": proc.get("WorkingSet", 0)
                        },
                        "host": {
                            "name": connection_info["vm_name"],
                            "id": connection_info["vm_name"]
                        },
                        "tags": ["process-monitoring", "vm-agent", connection_info["vm_name"]]
                    }
                    process_events.append(event)
                    
            except json.JSONDecodeError:
                print("   ⚠️ Failed to parse process JSON")
            
            return process_events
            
        except Exception as e:
            print(f"   ❌ Failed to collect process info: {e}")
            return []
    
    def collect_registry_changes(self, connection_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect actual registry changes from VM."""
        try:
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            ssh.connect(
                hostname=connection_info["host"],
                port=connection_info["port"],
                username=connection_info["user"],
                key_filename=connection_info["key_file"],
                timeout=30
            )
            
            # Get recent registry changes (simplified - would need more sophisticated monitoring)
            cmd = 'powershell -Command "Get-ChildItem HKLM:\\SOFTWARE -Recurse -ErrorAction SilentlyContinue | Where-Object {$_.PSChildName -like \'*Notepad*\' -or $_.PSChildName -like \'*TurdParty*\'} | Select-Object PSPath,PSChildName | ConvertTo-Json"'
            stdin, stdout, stderr = ssh.exec_command(cmd)
            registry_raw = stdout.read().decode('utf-8', errors='ignore')
            
            ssh.close()
            
            # Parse registry changes
            registry_events = []
            try:
                registry_items = json.loads(registry_raw)
                if not isinstance(registry_items, list):
                    registry_items = [registry_items]
                
                for item in registry_items:
                    event = {
                        "@timestamp": datetime.now().isoformat() + "Z",
                        "ecs": {"version": "8.11.0"},
                        "event": {
                            "kind": "event",
                            "category": ["configuration"],
                            "type": ["change"],
                            "action": "registry_key_found",
                            "outcome": "success"
                        },
                        "service": {
                            "name": "turdparty-vm-agent",
                            "type": "monitoring"
                        },
                        "registry": {
                            "key": item.get("PSPath", "unknown"),
                            "value": item.get("PSChildName", "unknown")
                        },
                        "host": {
                            "name": connection_info["vm_name"],
                            "id": connection_info["vm_name"]
                        },
                        "tags": ["registry-monitoring", "vm-agent", connection_info["vm_name"]]
                    }
                    registry_events.append(event)
                    
            except json.JSONDecodeError:
                print("   ⚠️ Failed to parse registry JSON")
            
            return registry_events
            
        except Exception as e:
            print(f"   ❌ Failed to collect registry changes: {e}")
            return []
    
    def send_events_to_elasticsearch(self, events: List[Dict[str, Any]], index_name: str) -> Dict[str, Any]:
        """Send events to Elasticsearch."""
        if not events:
            return {"success": True, "sent": 0, "total": 0}
        
        sent_count = 0
        errors = []
        
        for event in events:
            try:
                response = requests.post(
                    f"{self.es_url}/{index_name}/_doc",
                    json=event,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                
                if response.status_code in [200, 201]:
                    sent_count += 1
                else:
                    errors.append(f"HTTP {response.status_code}: {response.text}")
                    
            except Exception as e:
                errors.append(str(e))
        
        return {
            "success": sent_count > 0,
            "sent": sent_count,
            "total": len(events),
            "errors": errors[:5]  # Limit error list
        }
    
    def collect_all_data(self, vm_name: str, file_uuid: str) -> Dict[str, Any]:
        """Collect all ECS data from a VM."""
        print(f"📊 Collecting ECS data from VM: {vm_name}")
        
        # Get VM connection
        connection_info = self.get_vm_connection_info(vm_name)
        if not connection_info["success"]:
            return {
                "success": False,
                "error": f"Failed to connect to VM: {connection_info['error']}",
                "vm_name": vm_name
            }
        
        print(f"   ✅ Connected to VM: {connection_info['host']}:{connection_info['port']}")
        
        # Collect different types of data
        print("   📁 Collecting file changes...")
        file_events = self.collect_file_changes(connection_info)
        
        print("   🔄 Collecting process information...")
        process_events = self.collect_process_info(connection_info)
        
        print("   📝 Collecting registry changes...")
        registry_events = self.collect_registry_changes(connection_info)
        
        # Add file_uuid to all events
        all_events = file_events + process_events + registry_events
        for event in all_events:
            event["file_uuid"] = file_uuid
            event["vm_id"] = vm_name
        
        # Send to Elasticsearch
        index_name = f"turdparty-runtime-ecs-{datetime.now().strftime('%Y.%m.%d')}"
        print(f"   📤 Sending {len(all_events)} events to Elasticsearch...")
        
        es_result = self.send_events_to_elasticsearch(all_events, index_name)
        
        print(f"   ✅ Sent {es_result['sent']}/{es_result['total']} events to {index_name}")
        
        return {
            "success": True,
            "vm_name": vm_name,
            "file_uuid": file_uuid,
            "events_collected": {
                "file_events": len(file_events),
                "process_events": len(process_events),
                "registry_events": len(registry_events),
                "total": len(all_events)
            },
            "elasticsearch_result": es_result,
            "index_name": index_name
        }

def main():
    """Test ECS data collection."""
    collector = ECSDataCollector()
    
    # Example usage
    test_vm = "windows-test-vm"
    test_file_uuid = "test-uuid-12345"
    
    result = collector.collect_all_data(test_vm, test_file_uuid)
    
    print("\n" + "=" * 60)
    print("📊 ECS DATA COLLECTION RESULT")
    print("=" * 60)
    
    if result["success"]:
        print("✅ ECS data collection successful!")
        print(f"🎯 VM: {result['vm_name']}")
        print(f"📄 File UUID: {result['file_uuid']}")
        print(f"📁 File events: {result['events_collected']['file_events']}")
        print(f"🔄 Process events: {result['events_collected']['process_events']}")
        print(f"📝 Registry events: {result['events_collected']['registry_events']}")
        print(f"📊 Total events: {result['events_collected']['total']}")
        print(f"📤 Elasticsearch: {result['elasticsearch_result']['sent']}/{result['elasticsearch_result']['total']} sent")
        print(f"🗂️ Index: {result['index_name']}")
    else:
        print(f"❌ Collection failed: {result['error']}")
    
    return result

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Complete Notepad++ Installation Workflow Test
Tests the complete end-to-end workflow for Notepad++ installation in Windows VMs.

This implements the complete PRD requirements from PRD-Notepadpp-API-Workflow-Testing.md:
- Download Notepad++ binary
- Upload to MinIO via API
- Create Windows VM via API
- Wait for VM to be ready
- Inject file into VM via gRPC
- Execute installer in VM
- Verify installation success
- Monitor and log all steps

No mocks, simulations, or emulation - production code only.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import utilities for dynamic module loading
import importlib.util
from services.workers.tasks.file_injection import FileInjectionService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CompleteNotepadppWorkflow:
    """Complete Notepad++ installation workflow orchestrator."""
    
    def __init__(self):
        """Initialize the workflow orchestrator."""
        self.base_url = "http://localhost:8000"
        self.file_injection_service = FileInjectionService()
        
    async def execute_complete_workflow(self) -> Dict[str, Any]:
        """Execute the complete Notepad++ installation workflow.
        
        Returns:
            Complete workflow results.
        """
        logger.info("🚀 Starting Complete Notepad++ Installation Workflow")
        logger.info("📋 PRD: PRD-Notepadpp-API-Workflow-Testing.md")
        logger.info("🚫 No mocks, simulations, or emulation - production code only")
        
        workflow_start_time = time.time()
        results = {}
        
        try:
            # Step 1: Download Notepad++ Binary
            logger.info("\n📥 STEP 1: Download Notepad++ Binary")
            download_result = await self._download_binary()
            results["download"] = download_result
            
            if not download_result["success"]:
                return self._create_failure_result("Binary download failed", results)
            
            # Step 2: Upload to MinIO via API
            logger.info("\n📤 STEP 2: Upload to MinIO via API")
            upload_result = await self._upload_to_minio(download_result["file_path"])
            results["upload"] = upload_result
            
            if not upload_result["success"]:
                return self._create_failure_result("File upload failed", results)
            
            # Step 3: Create Windows VM via API
            logger.info("\n🪟 STEP 3: Create Windows VM via API")
            vm_result = await self._create_windows_vm()
            results["vm_creation"] = vm_result
            
            if not vm_result["success"]:
                return self._create_failure_result("VM creation failed", results)
            
            # Step 4: Wait for VM to be ready
            logger.info("\n⏳ STEP 4: Wait for VM to be ready")
            vm_ready_result = await self._wait_for_vm_ready(vm_result["vm_id"])
            results["vm_ready"] = vm_ready_result
            
            if not vm_ready_result["success"]:
                return self._create_failure_result("VM failed to become ready", results)
            
            # Step 5: Inject and Execute Notepad++ Installer
            logger.info("\n🔧 STEP 5: Inject and Execute Notepad++ Installer")
            installation_result = await self._inject_and_execute_installer(
                vm_result["vm_id"], upload_result["file_id"]
            )
            results["installation"] = installation_result
            
            if not installation_result["success"]:
                return self._create_failure_result("Installation failed", results)
            
            # Step 6: Verify Installation
            logger.info("\n✅ STEP 6: Verify Installation Success")
            verification_result = await self._verify_installation(
                vm_result["vm_id"], installation_result
            )
            results["verification"] = verification_result
            
            total_time = time.time() - workflow_start_time
            
            # Create success result
            final_result = {
                "success": True,
                "workflow_type": "complete_notepadpp_installation",
                "total_time": total_time,
                "vm_id": vm_result["vm_id"],
                "file_id": upload_result["file_id"],
                "installation_verified": verification_result.get("installation_verified", False),
                "steps": results,
                "message": f"Complete Notepad++ installation workflow completed successfully in {total_time:.2f} seconds"
            }
            
            logger.info(f"🎉 WORKFLOW COMPLETED SUCCESSFULLY!")
            logger.info(f"⏱️  Total time: {total_time:.2f} seconds")
            logger.info(f"🆔 VM ID: {vm_result['vm_id']}")
            logger.info(f"📄 File ID: {upload_result['file_id']}")
            logger.info(f"✅ Installation verified: {verification_result.get('installation_verified', False)}")
            
            return final_result
            
        except Exception as e:
            logger.error(f"❌ Workflow failed with exception: {e}")
            return self._create_failure_result(f"Workflow exception: {str(e)}", results)
    
    async def _download_binary(self) -> Dict[str, Any]:
        """Download Notepad++ binary."""
        try:
            # Import and use the downloader class directly
            spec = importlib.util.spec_from_file_location("download_notepadpp_binary",
                                                        Path(__file__).parent / "download-notepadpp-binary.py")
            download_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(download_module)

            # Create downloader instance and download
            downloader = download_module.NotepadppDownloader()
            result = await downloader.download_binary()
            await downloader.cleanup()

            return {
                "success": True,
                "file_path": result["file_path"],
                "file_size": result["file_size"],
                "blake3_hash": result["blake3_hash"],
                "download_time": result["download_time"]
            }
        except Exception as e:
            logger.error(f"❌ Binary download failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _upload_to_minio(self, file_path: str) -> Dict[str, Any]:
        """Upload file to MinIO via API."""
        try:
            import httpx
            
            logger.info(f"📤 Uploading file via /api/v1/files/upload")
            upload_url = f"{self.base_url}/api/v1/files/upload"
            
            async with httpx.AsyncClient(timeout=300.0) as client:
                with open(file_path, "rb") as f:
                    files = {"file": ("npp.8.5.8.Installer.x64.exe", f, "application/octet-stream")}
                    response = await client.post(upload_url, files=files)
                    response.raise_for_status()
                    
                    upload_data = response.json()
                    file_id = upload_data["file_id"]
                    
                    logger.info(f"✅ Upload successful - File ID: {file_id}")
                    
                    return {
                        "success": True,
                        "file_id": file_id,
                        "upload_data": upload_data
                    }
                    
        except Exception as e:
            logger.error(f"❌ Upload failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _create_windows_vm(self) -> Dict[str, Any]:
        """Create Windows VM via API."""
        try:
            import httpx
            
            vm_name = f"notepadpp-install-{int(time.time())}"
            
            vm_data = {
                "name": vm_name,
                "template": "gusztavvargadr/windows-10",
                "vm_type": "vagrant",
                "memory_mb": 4096,
                "cpus": 2,
                "disk_gb": 40,
                "domain": "TurdParty",
                "description": f"Notepad++ installation test - {vm_name}",
                "auto_start": True
            }
            
            logger.info(f"🪟 Creating Windows VM: {vm_name}")
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(f"{self.base_url}/api/v1/vms/", json=vm_data)
                response.raise_for_status()
                
                vm_response = response.json()
                vm_id = vm_response["id"]
                
                logger.info(f"✅ VM created - ID: {vm_id}")
                
                return {
                    "success": True,
                    "vm_id": vm_id,
                    "vm_name": vm_name,
                    "vm_data": vm_response
                }
                
        except Exception as e:
            logger.error(f"❌ VM creation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _wait_for_vm_ready(self, vm_id: str, timeout: int = 1800) -> Dict[str, Any]:
        """Wait for VM to be ready for operations."""
        try:
            import httpx
            
            logger.info(f"⏳ Waiting for VM {vm_id} to be ready (timeout: {timeout}s)")
            
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                while time.time() - start_time < timeout:
                    try:
                        response = await client.get(f"{self.base_url}/api/v1/vms/{vm_id}")
                        response.raise_for_status()
                        
                        vm_status = response.json()
                        status = vm_status.get("status", "unknown")
                        
                        logger.info(f"📊 VM Status: {status}")
                        
                        if status == "running":
                            logger.info(f"✅ VM is ready for operations")
                            return {
                                "success": True,
                                "vm_id": vm_id,
                                "status": status,
                                "wait_time": time.time() - start_time
                            }
                        elif status == "error":
                            return {
                                "success": False,
                                "error": "VM entered error state",
                                "vm_id": vm_id,
                                "status": status
                            }
                        
                        await asyncio.sleep(30)  # Check every 30 seconds
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Status check failed: {e}")
                        await asyncio.sleep(10)
                
                return {
                    "success": False,
                    "error": "VM ready timeout exceeded",
                    "vm_id": vm_id,
                    "timeout": timeout
                }
                
        except Exception as e:
            logger.error(f"❌ VM ready check failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "vm_id": vm_id
            }
    
    async def _inject_and_execute_installer(self, vm_id: str, file_id: str) -> Dict[str, Any]:
        """Inject and execute Notepad++ installer in VM."""
        try:
            logger.info(f"🔧 Starting Notepad++ installation in VM {vm_id}")
            
            # Use the specialized Notepad++ installation method
            result = await self.file_injection_service.inject_and_execute_file(
                vm_id=vm_id,
                file_id=file_id,
                destination_path="C:\\temp\\npp.8.5.8.Installer.x64.exe",
                execution_args=["/S"],  # Silent installation
                execution_timeout=600   # 10 minutes
            )
            
            if result["success"]:
                logger.info("✅ Notepad++ installation completed successfully")
            else:
                logger.error(f"❌ Installation failed: {result.get('error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Installation process failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "vm_id": vm_id,
                "file_id": file_id
            }
    
    async def _verify_installation(self, vm_id: str, installation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Verify Notepad++ installation was successful."""
        try:
            logger.info(f"🔍 Verifying Notepad++ installation in VM {vm_id}")
            
            # Check if installation verification was already done
            execution_result = installation_result.get("execution_result", {})
            if "installation_verified" in execution_result:
                verification_data = execution_result["installation_verified"]
                
                logger.info(f"✅ Installation verification: {verification_data.get('installation_verified', False)}")
                
                return {
                    "success": True,
                    "installation_verified": verification_data.get("installation_verified", False),
                    "verification_details": verification_data.get("verification_details", {}),
                    "vm_id": vm_id
                }
            else:
                logger.warning("⚠️ No installation verification data available")
                return {
                    "success": False,
                    "error": "No installation verification data available",
                    "vm_id": vm_id
                }
                
        except Exception as e:
            logger.error(f"❌ Installation verification failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "vm_id": vm_id
            }
    
    def _create_failure_result(self, error_message: str, partial_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create a failure result with partial results."""
        return {
            "success": False,
            "error": error_message,
            "partial_results": partial_results,
            "workflow_type": "complete_notepadpp_installation"
        }


async def main():
    """Main function to run the complete workflow test."""
    workflow = CompleteNotepadppWorkflow()
    result = await workflow.execute_complete_workflow()
    
    if result["success"]:
        logger.info("🎉 Complete workflow test PASSED")
        return 0
    else:
        logger.error("❌ Complete workflow test FAILED")
        logger.error(f"Error: {result.get('error')}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

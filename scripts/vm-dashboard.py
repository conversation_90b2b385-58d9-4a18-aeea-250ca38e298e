#!/usr/bin/env python3
"""
TurdParty VM Dashboard with Rich Interface
Beautiful real-time dashboard for monitoring VM status with rich visual components.

This provides a professional dashboard interface using the Rich library for:
- Real-time VM status monitoring
- Visual progress bars and status indicators
- Live updating panels with detailed information
- Color-coded status displays
- Professional terminal UI
"""

import asyncio
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

try:
    import httpx
    from rich.console import Console
    from rich.layout import Layout
    from rich.panel import Panel
    from rich.progress import Progress, BarColumn, TextColumn, TimeRemainingColumn, SpinnerColumn
    from rich.table import Table
    from rich.text import Text
    from rich.live import Live
    from rich.align import Align
    from rich.columns import Columns
    from rich import box
except ImportError as e:
    print(f"❌ Required packages missing: {e}")
    print("Run with: nix-shell -p python311 python311Packages.httpx python311Packages.rich --run 'python scripts/vm-dashboard.py <vm_id>'")
    sys.exit(1)

class VMDashboard:
    """Rich-based VM monitoring dashboard."""
    
    def __init__(self, vm_id: str, base_url: str = "http://localhost:8000"):
        """Initialize the dashboard.
        
        Args:
            vm_id: VM identifier to monitor.
            base_url: API base URL.
        """
        self.vm_id = vm_id
        self.base_url = base_url
        self.console = Console()
        self.start_time = time.time()
        self.check_count = 0
        self.status_history = []
        self.last_status = None
        
    async def run_dashboard(self, max_duration: int = 1800, refresh_interval: int = 5):
        """Run the dashboard with live updates.
        
        Args:
            max_duration: Maximum monitoring duration in seconds.
            refresh_interval: Refresh interval in seconds.
        """
        with Live(self._create_layout(), refresh_per_second=1, screen=True) as live:
            async with httpx.AsyncClient(timeout=30.0) as client:
                while time.time() - self.start_time < max_duration:
                    try:
                        # Fetch VM status
                        status_data = await self._fetch_vm_status(client)
                        
                        if status_data:
                            self.status_history.append({
                                "timestamp": time.time(),
                                "data": status_data
                            })
                            self.last_status = status_data
                            
                            # Keep only last 50 entries
                            if len(self.status_history) > 50:
                                self.status_history = self.status_history[-50:]
                        
                        self.check_count += 1
                        
                        # Update the layout
                        live.update(self._create_layout())
                        
                        # Check for completion
                        if status_data and self._is_complete(status_data):
                            await asyncio.sleep(5)  # Show final state for 5 seconds
                            break
                        
                        await asyncio.sleep(refresh_interval)
                        
                    except KeyboardInterrupt:
                        break
                    except Exception as e:
                        self.console.print(f"[red]Error: {e}[/red]")
                        await asyncio.sleep(refresh_interval)
    
    async def _fetch_vm_status(self, client: httpx.AsyncClient) -> Optional[Dict[str, Any]]:
        """Fetch VM status from API.
        
        Args:
            client: HTTP client.
            
        Returns:
            VM status data or None if failed.
        """
        try:
            response = await client.get(f"{self.base_url}/api/v1/vms/{self.vm_id}/status")
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                return {"error": "VM not found", "status": "not_found"}
            else:
                return {"error": f"API Error {response.status_code}", "status": "api_error"}
        except Exception as e:
            return {"error": str(e), "status": "connection_error"}
    
    def _create_layout(self) -> Layout:
        """Create the dashboard layout.
        
        Returns:
            Rich Layout object.
        """
        layout = Layout()
        
        # Split into header, main, and footer
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )
        
        # Split main into left and right
        layout["main"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        # Split left into status and progress
        layout["left"].split_column(
            Layout(name="status", size=12),
            Layout(name="progress")
        )
        
        # Split right into details and history
        layout["right"].split_column(
            Layout(name="details"),
            Layout(name="history", size=15)
        )
        
        # Populate layouts
        layout["header"].update(self._create_header())
        layout["status"].update(self._create_status_panel())
        layout["progress"].update(self._create_progress_panel())
        layout["details"].update(self._create_details_panel())
        layout["history"].update(self._create_history_panel())
        layout["footer"].update(self._create_footer())
        
        return layout
    
    def _create_header(self) -> Panel:
        """Create header panel.
        
        Returns:
            Rich Panel with header information.
        """
        elapsed = time.time() - self.start_time
        elapsed_str = str(timedelta(seconds=int(elapsed)))
        
        header_text = Text()
        header_text.append("💩🎉 TurdParty VM Dashboard 🎉💩", style="bold magenta")
        header_text.append(f"\nVM ID: {self.vm_id}", style="cyan")
        header_text.append(f" | Elapsed: {elapsed_str}", style="green")
        header_text.append(f" | Checks: {self.check_count}", style="yellow")
        
        return Panel(
            Align.center(header_text),
            box=box.DOUBLE,
            style="bright_blue"
        )
    
    def _create_status_panel(self) -> Panel:
        """Create status panel.
        
        Returns:
            Rich Panel with current status.
        """
        if not self.last_status:
            return Panel(
                Align.center(Text("🔄 Loading VM status...", style="yellow")),
                title="VM Status",
                box=box.ROUNDED
            )
        
        if "error" in self.last_status:
            error_text = Text()
            error_text.append("❌ Error\n", style="bold red")
            error_text.append(self.last_status["error"], style="red")
            return Panel(
                Align.center(error_text),
                title="VM Status",
                box=box.ROUNDED,
                style="red"
            )
        
        status = self.last_status.get("status", "unknown").upper()
        completion = self.last_status.get("completion_percentage", 0)
        runtime = self.last_status.get("runtime_minutes", 0)
        
        # Status color mapping
        status_colors = {
            "CREATING": "yellow",
            "PROVISIONING": "blue",
            "CONFIGURING": "cyan",
            "STARTING": "magenta",
            "RUNNING": "green",
            "ERROR": "red",
            "STOPPED": "white",
            "TERMINATED": "white"
        }
        
        status_color = status_colors.get(status, "white")
        
        status_text = Text()
        status_text.append(f"Status: {status}\n", style=f"bold {status_color}")
        status_text.append(f"Progress: {completion}%\n", style="bright_white")
        status_text.append(f"Runtime: {runtime:.1f} min\n", style="bright_white")
        
        # Add platform info
        details = self.last_status.get("status_details", {})
        platform = details.get("platform", "Unknown")
        phase = details.get("phase", "unknown")
        
        status_text.append(f"Platform: {platform}\n", style="cyan")
        status_text.append(f"Phase: {phase}", style="magenta")
        
        return Panel(
            status_text,
            title="🖥️  VM Status",
            box=box.ROUNDED,
            style=status_color
        )
    
    def _create_progress_panel(self) -> Panel:
        """Create progress panel with visual progress bar.
        
        Returns:
            Rich Panel with progress information.
        """
        if not self.last_status or "error" in self.last_status:
            return Panel(
                Text("No progress data available", style="dim"),
                title="Progress",
                box=box.ROUNDED
            )
        
        completion = self.last_status.get("completion_percentage", 0)
        details = self.last_status.get("status_details", {})
        
        # Create progress bar
        progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(bar_width=30),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeRemainingColumn(),
            expand=False
        )
        
        # Determine if actively progressing
        is_progressing = details.get("is_progressing", False)
        
        if is_progressing:
            task = progress.add_task("Provisioning VM...", total=100, completed=completion)
        else:
            task = progress.add_task("VM Status", total=100, completed=completion)
        
        # Add description and next step
        description = details.get("description", "")
        next_step = details.get("next_step", "")
        estimated = details.get("estimated_remaining", "")
        
        progress_text = Text()
        progress_text.append(f"📝 {description}\n\n", style="bright_white")
        progress_text.append(f"➡️  Next: {next_step}\n", style="cyan")
        progress_text.append(f"⏱️  Est. Remaining: {estimated}", style="yellow")
        
        progress_content = Columns([
            Panel(progress, box=box.SIMPLE),
            Panel(progress_text, box=box.SIMPLE)
        ])
        
        return Panel(
            progress_content,
            title="📊 Progress Details",
            box=box.ROUNDED
        )
    
    def _create_details_panel(self) -> Panel:
        """Create details panel.
        
        Returns:
            Rich Panel with detailed VM information.
        """
        if not self.last_status or "error" in self.last_status:
            return Panel(
                Text("No details available", style="dim"),
                title="Details",
                box=box.ROUNDED
            )
        
        table = Table(show_header=False, box=box.SIMPLE, padding=(0, 1))
        table.add_column("Property", style="cyan", width=15)
        table.add_column("Value", style="bright_white")
        
        # Add VM details
        table.add_row("Name", self.last_status.get("name", "Unknown"))
        table.add_row("Template", self.last_status.get("template", "Unknown"))
        table.add_row("IP Address", self.last_status.get("ip_address", "Not assigned"))
        table.add_row("SSH Port", str(self.last_status.get("ssh_port", "Not assigned")))
        
        # Add timestamps
        created_at = self.last_status.get("created_at", "")
        if created_at:
            try:
                created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                table.add_row("Created", created_time.strftime("%H:%M:%S"))
            except:
                table.add_row("Created", created_at)
        
        updated_at = self.last_status.get("updated_at", "")
        if updated_at:
            try:
                updated_time = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                table.add_row("Updated", updated_time.strftime("%H:%M:%S"))
            except:
                table.add_row("Updated", updated_at)
        
        return Panel(
            table,
            title="🔍 VM Details",
            box=box.ROUNDED
        )
    
    def _create_history_panel(self) -> Panel:
        """Create history panel.
        
        Returns:
            Rich Panel with status history.
        """
        if not self.status_history:
            return Panel(
                Text("No history data yet...", style="dim"),
                title="Status History",
                box=box.ROUNDED
            )
        
        table = Table(box=box.SIMPLE, padding=(0, 1))
        table.add_column("Time", style="cyan", width=8)
        table.add_column("Status", style="bright_white", width=12)
        table.add_column("Progress", style="green", width=8)
        table.add_column("Phase", style="magenta")
        
        # Show last 10 entries
        recent_history = self.status_history[-10:]
        
        for entry in recent_history:
            timestamp = entry["timestamp"]
            data = entry["data"]
            
            if "error" in data:
                continue
            
            time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M:%S")
            status = data.get("status", "unknown")
            progress = f"{data.get('completion_percentage', 0)}%"
            phase = data.get("status_details", {}).get("phase", "unknown")
            
            table.add_row(time_str, status, progress, phase)
        
        return Panel(
            table,
            title="📈 Status History",
            box=box.ROUNDED
        )
    
    def _create_footer(self) -> Panel:
        """Create footer panel.
        
        Returns:
            Rich Panel with footer information.
        """
        footer_text = Text()
        footer_text.append("Press Ctrl+C to exit", style="dim")
        footer_text.append(" | ", style="dim")
        footer_text.append("Refreshing every 5 seconds", style="dim")
        footer_text.append(" | ", style="dim")
        footer_text.append(f"Last update: {datetime.now().strftime('%H:%M:%S')}", style="dim")
        
        return Panel(
            Align.center(footer_text),
            box=box.SIMPLE,
            style="dim"
        )
    
    def _is_complete(self, status_data: Dict[str, Any]) -> bool:
        """Check if VM is in a completed state.
        
        Args:
            status_data: VM status data.
            
        Returns:
            True if VM is complete, False otherwise.
        """
        if "error" in status_data:
            return True
        
        status = status_data.get("status", "").lower()
        completion = status_data.get("completion_percentage", 0)
        
        return (
            status in ["running", "stopped", "terminated", "error"] or
            completion >= 100
        )


async def main():
    """Main function."""
    if len(sys.argv) != 2:
        console = Console()
        console.print("[red]Usage:[/red] python scripts/vm-dashboard.py <vm_id>")
        console.print("[yellow]Example:[/yellow] python scripts/vm-dashboard.py 16bb5acf-23b9-445f-8a95-c606ab182b3d")
        return 1
    
    vm_id = sys.argv[1]
    dashboard = VMDashboard(vm_id)
    
    try:
        await dashboard.run_dashboard()
        return 0
    except KeyboardInterrupt:
        console = Console()
        console.print("\n[yellow]Dashboard stopped by user[/yellow]")
        return 0
    except Exception as e:
        console = Console()
        console.print(f"[red]Dashboard error: {e}[/red]")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

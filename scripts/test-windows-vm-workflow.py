#!/usr/bin/env python3
"""
Windows VM Workflow Test
Tests the Windows VM creation and gRPC communication workflow.

This validates the Windows VM support and gRPC infrastructure we implemented:
- Create Windows VM via API
- Monitor VM status
- Test gRPC communication readiness
- Validate Windows-specific configuration

No external dependencies beyond httpx and standard library.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WindowsVMWorkflowTest:
    """Test Windows VM workflow functionality."""
    
    def __init__(self):
        """Initialize the test."""
        self.base_url = "http://localhost:8000"
        
    async def test_windows_vm_workflow(self) -> Dict[str, Any]:
        """Test complete Windows VM workflow.
        
        Returns:
            Test results.
        """
        logger.info("🚀 Starting Windows VM Workflow Test")
        logger.info("🪟 Testing Windows VM support via Vagrant gRPC API")
        
        test_start_time = time.time()
        results = {}
        
        try:
            # Step 1: Check API Health
            logger.info("\n🏥 STEP 1: Check API Health")
            health_result = await self._check_api_health()
            results["api_health"] = health_result
            
            if not health_result["success"]:
                return self._create_failure_result("API health check failed", results)
            
            # Step 2: Check Windows Templates Available
            logger.info("\n📋 STEP 2: Check Windows Templates")
            templates_result = await self._check_windows_templates()
            results["templates"] = templates_result
            
            if not templates_result["success"]:
                return self._create_failure_result("Windows templates not available", results)
            
            # Step 3: Create Windows VM
            logger.info("\n🪟 STEP 3: Create Windows VM")
            vm_result = await self._create_windows_vm()
            results["vm_creation"] = vm_result
            
            if not vm_result["success"]:
                return self._create_failure_result("Windows VM creation failed", results)
            
            # Step 4: Monitor VM Status
            logger.info("\n👀 STEP 4: Monitor VM Status")
            monitoring_result = await self._monitor_vm_status(vm_result["vm_id"])
            results["monitoring"] = monitoring_result
            
            # Step 5: Test gRPC Readiness (simulated)
            logger.info("\n🔌 STEP 5: Test gRPC Readiness")
            grpc_result = await self._test_grpc_readiness(vm_result["vm_id"])
            results["grpc_readiness"] = grpc_result
            
            total_time = time.time() - test_start_time
            
            # Create success result
            final_result = {
                "success": True,
                "test_type": "windows_vm_workflow",
                "total_time": total_time,
                "vm_id": vm_result["vm_id"],
                "vm_status": monitoring_result.get("final_status", "unknown"),
                "windows_template": vm_result.get("template"),
                "steps": results,
                "message": f"Windows VM workflow test completed in {total_time:.2f} seconds"
            }
            
            logger.info(f"🎉 WINDOWS VM WORKFLOW TEST COMPLETED!")
            logger.info(f"⏱️  Total time: {total_time:.2f} seconds")
            logger.info(f"🆔 VM ID: {vm_result['vm_id']}")
            logger.info(f"📊 Final Status: {monitoring_result.get('final_status', 'unknown')}")
            
            return final_result
            
        except Exception as e:
            logger.error(f"❌ Test failed with exception: {e}")
            return self._create_failure_result(f"Test exception: {str(e)}", results)
    
    async def _check_api_health(self) -> Dict[str, Any]:
        """Check API health."""
        try:
            import httpx
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/health/")
                response.raise_for_status()
                
                health_data = response.json()
                logger.info(f"✅ API is healthy: {health_data}")
                
                return {
                    "success": True,
                    "health_data": health_data
                }
                
        except Exception as e:
            logger.error(f"❌ API health check failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _check_windows_templates(self) -> Dict[str, Any]:
        """Check if Windows templates are available."""
        try:
            import httpx
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{self.base_url}/api/v1/vms/templates")
                response.raise_for_status()
                
                templates = response.json()
                
                # Check for Windows templates
                windows_templates = [
                    t for t in templates 
                    if "WINDOWS" in t.get("name", "").upper()
                ]
                
                logger.info(f"📋 Found {len(windows_templates)} Windows templates")
                for template in windows_templates:
                    logger.info(f"   - {template['name']}: {template['value']}")
                
                if not windows_templates:
                    return {
                        "success": False,
                        "error": "No Windows templates found",
                        "available_templates": [t.get("name") for t in templates]
                    }
                
                return {
                    "success": True,
                    "windows_templates": windows_templates,
                    "total_templates": len(templates)
                }
                
        except Exception as e:
            logger.error(f"❌ Templates check failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _create_windows_vm(self) -> Dict[str, Any]:
        """Create Windows VM."""
        try:
            import httpx
            
            vm_name = f"windows-test-{int(time.time())}"
            
            vm_data = {
                "name": vm_name,
                "template": "gusztavvargadr/windows-10",
                "vm_type": "vagrant",
                "memory_mb": 4096,
                "cpus": 2,
                "disk_gb": 40,
                "domain": "TurdParty",
                "description": f"Windows VM workflow test - {vm_name}",
                "auto_start": True
            }
            
            logger.info(f"🪟 Creating Windows VM: {vm_name}")
            logger.info(f"📋 Template: {vm_data['template']}")
            logger.info(f"💾 Memory: {vm_data['memory_mb']} MB")
            logger.info(f"🔧 CPUs: {vm_data['cpus']}")
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(f"{self.base_url}/api/v1/vms/", json=vm_data)
                
                if response.status_code != 201:
                    logger.error(f"❌ VM creation failed with status {response.status_code}")
                    logger.error(f"Response: {response.text}")
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "vm_data": vm_data
                    }
                
                vm_response = response.json()
                vm_id = vm_response["vm_id"]
                
                logger.info(f"✅ Windows VM created successfully")
                logger.info(f"🆔 VM ID: {vm_id}")
                
                return {
                    "success": True,
                    "vm_id": vm_id,
                    "vm_name": vm_name,
                    "template": vm_data["template"],
                    "vm_response": vm_response
                }
                
        except Exception as e:
            logger.error(f"❌ Windows VM creation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _monitor_vm_status(self, vm_id: str, max_checks: int = 10) -> Dict[str, Any]:
        """Monitor VM status for a limited time."""
        try:
            import httpx
            
            logger.info(f"👀 Monitoring VM {vm_id} status (max {max_checks} checks)")
            
            status_history = []
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                for check_num in range(max_checks):
                    try:
                        response = await client.get(f"{self.base_url}/api/v1/vms/{vm_id}")
                        response.raise_for_status()
                        
                        vm_status = response.json()
                        status = vm_status.get("status", "unknown")
                        
                        status_history.append({
                            "check_num": check_num + 1,
                            "status": status,
                            "timestamp": time.time()
                        })
                        
                        logger.info(f"📊 Check {check_num + 1}/{max_checks}: Status = {status}")
                        
                        if status == "running":
                            logger.info(f"✅ VM is running after {check_num + 1} checks")
                            break
                        elif status == "error":
                            logger.error(f"❌ VM entered error state")
                            break
                        
                        if check_num < max_checks - 1:
                            await asyncio.sleep(10)  # Wait 10 seconds between checks
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Status check {check_num + 1} failed: {e}")
                        status_history.append({
                            "check_num": check_num + 1,
                            "status": "check_failed",
                            "error": str(e),
                            "timestamp": time.time()
                        })
                
                final_status = status_history[-1]["status"] if status_history else "unknown"
                
                return {
                    "success": True,
                    "vm_id": vm_id,
                    "final_status": final_status,
                    "total_checks": len(status_history),
                    "status_history": status_history
                }
                
        except Exception as e:
            logger.error(f"❌ VM monitoring failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "vm_id": vm_id
            }
    
    async def _test_grpc_readiness(self, vm_id: str) -> Dict[str, Any]:
        """Test gRPC readiness (simulated for now)."""
        try:
            logger.info(f"🔌 Testing gRPC readiness for VM {vm_id}")
            
            # Simulate gRPC readiness check
            await asyncio.sleep(1)
            
            # For now, simulate successful gRPC readiness
            grpc_ready = True
            grpc_port = 40000
            
            logger.info(f"✅ gRPC readiness check completed")
            logger.info(f"🔌 gRPC Port: {grpc_port}")
            logger.info(f"📡 Ready for communication: {grpc_ready}")
            
            return {
                "success": True,
                "vm_id": vm_id,
                "grpc_ready": grpc_ready,
                "grpc_port": grpc_port,
                "message": "gRPC readiness check completed (simulated)"
            }
            
        except Exception as e:
            logger.error(f"❌ gRPC readiness test failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "vm_id": vm_id
            }
    
    def _create_failure_result(self, error_message: str, partial_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create a failure result with partial results."""
        return {
            "success": False,
            "error": error_message,
            "partial_results": partial_results,
            "test_type": "windows_vm_workflow"
        }


async def main():
    """Main function to run the Windows VM workflow test."""
    test = WindowsVMWorkflowTest()
    result = await test.test_windows_vm_workflow()
    
    if result["success"]:
        logger.info("🎉 Windows VM workflow test PASSED")
        return 0
    else:
        logger.error("❌ Windows VM workflow test FAILED")
        logger.error(f"Error: {result.get('error')}")
        return 1


if __name__ == "__main__":
    try:
        import httpx
    except ImportError:
        print("❌ httpx package required. Run with: nix-shell -p python311 python311Packages.httpx --run 'python scripts/test-windows-vm-workflow.py'")
        sys.exit(1)
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

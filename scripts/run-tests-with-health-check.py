#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Parallel Test Runner with Health Checks

This script runs service health checks using the centralized URL system
before executing other tests in parallel. It ensures all required services
are healthy and provides detailed reporting.
"""

import asyncio
import subprocess
import sys
import time
import argparse
from pathlib import Path
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from tests.health.test_service_uptime import ServiceUptimeChecker


class ParallelTestRunner:
    """
    Test runner that performs health checks before running tests in parallel
    """
    
    def __init__(self, max_workers: int = 6, health_timeout: int = 30):
        """
        Initialize the test runner
        
        Args:
            max_workers: Maximum number of parallel test workers
            health_timeout: Timeout for health checks in seconds
        """
        self.max_workers = max_workers
        self.health_timeout = health_timeout
        self.uptime_checker = ServiceUptimeChecker(timeout=10, max_retries=2)
        
        # Define test suites that can run in parallel
        self.test_suites = [
            {
                'name': 'Unit Tests',
                'command': ['python', '-m', 'pytest', 'tests/unit/', '-v', '--tb=short'],
                'timeout': 300,
                'critical': True
            },
            {
                'name': 'Integration Tests',
                'command': ['python', '-m', 'pytest', 'tests/integration/', '-v', '--tb=short', '-m', 'not real'],
                'timeout': 600,
                'critical': True
            },
            {
                'name': 'Health Tests',
                'command': ['python', '-m', 'pytest', 'tests/health/', '-v', '--tb=short'],
                'timeout': 120,
                'critical': True
            },
            {
                'name': 'API Tests',
                'command': ['python', '-m', 'pytest', 'tests/api/', '-v', '--tb=short'],
                'timeout': 300,
                'critical': False
            },
            {
                'name': 'Performance Tests',
                'command': ['python', '-m', 'pytest', 'tests/performance/', '-v', '--tb=short', '--benchmark-skip'],
                'timeout': 180,
                'critical': False
            },
            {
                'name': 'Property Tests',
                'command': ['python', '-m', 'pytest', 'tests/property/', '-v', '--tb=short'],
                'timeout': 240,
                'critical': False
            }
        ]
    
    async def run_health_checks(self) -> bool:
        """
        Run comprehensive health checks before starting tests
        
        Returns:
            True if all critical services are healthy
        """
        print("🏥 Running pre-test health checks...")
        print("=" * 60)
        
        try:
            # Run health checks with timeout
            results = await asyncio.wait_for(
                self.uptime_checker.check_all_services(),
                timeout=self.health_timeout
            )
            
            # Analyze results
            all_critical_healthy, critical_failures, warnings = \
                self.uptime_checker.analyze_health_results(results)
            
            # Print detailed health report
            self.print_health_report(results, critical_failures, warnings)
            
            if not all_critical_healthy:
                print("\n❌ Critical services are not healthy!")
                print("Cannot proceed with testing until services are fixed.")
                return False
            
            print("\n✅ All critical services are healthy!")
            print("Proceeding with parallel test execution...")
            return True
        
        except asyncio.TimeoutError:
            print(f"\n⏰ Health check timeout after {self.health_timeout}s")
            print("Services may be starting up or experiencing issues.")
            return False
        
        except Exception as e:
            print(f"\n💥 Health check failed: {e}")
            return False
    
    def print_health_report(self, results: Dict, critical_failures: List[str], warnings: List[str]):
        """Print detailed health check report"""
        print(f"\n📊 Service Health Report:")
        print(f"Environment: {self.uptime_checker.url_manager.get_current_environment()}")
        print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total services: {len(results)}")
        
        # Group results by status
        healthy_services = []
        unhealthy_services = []
        
        for service_name, result in results.items():
            if result.status == 'healthy':
                healthy_services.append((service_name, result))
            else:
                unhealthy_services.append((service_name, result))
        
        # Print healthy services
        if healthy_services:
            print(f"\n✅ Healthy Services ({len(healthy_services)}):")
            for service_name, result in healthy_services:
                print(f"  ✅ {service_name}: {result.response_time:.2f}s")
        
        # Print unhealthy services
        if unhealthy_services:
            print(f"\n❌ Unhealthy Services ({len(unhealthy_services)}):")
            for service_name, result in unhealthy_services:
                status_emoji = {
                    'unhealthy': '❌',
                    'timeout': '⏰',
                    'error': '💥'
                }.get(result.status, '❓')
                print(f"  {status_emoji} {service_name}: {result.status}")
                if result.error_message:
                    print(f"    └─ {result.error_message}")
        
        # Print warnings and critical failures
        if warnings:
            print(f"\n⚠️ Warnings ({len(warnings)}):")
            for warning in warnings:
                print(f"  ⚠️ {warning}")
        
        if critical_failures:
            print(f"\n🚨 Critical Failures ({len(critical_failures)}):")
            for failure in critical_failures:
                print(f"  🚨 {failure}")
    
    def run_test_suite(self, test_suite: Dict) -> Dict:
        """
        Run a single test suite
        
        Args:
            test_suite: Test suite configuration
            
        Returns:
            Test result dictionary
        """
        print(f"🧪 Starting: {test_suite['name']}")
        start_time = time.time()
        
        try:
            # Run the test command
            result = subprocess.run(
                test_suite['command'],
                capture_output=True,
                text=True,
                timeout=test_suite['timeout'],
                cwd=Path(__file__).parent.parent
            )
            
            duration = time.time() - start_time
            
            return {
                'name': test_suite['name'],
                'success': result.returncode == 0,
                'duration': duration,
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'critical': test_suite['critical']
            }
        
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            return {
                'name': test_suite['name'],
                'success': False,
                'duration': duration,
                'returncode': -1,
                'stdout': '',
                'stderr': f"Test suite timed out after {test_suite['timeout']}s",
                'critical': test_suite['critical']
            }
        
        except Exception as e:
            duration = time.time() - start_time
            return {
                'name': test_suite['name'],
                'success': False,
                'duration': duration,
                'returncode': -1,
                'stdout': '',
                'stderr': f"Error running test suite: {e}",
                'critical': test_suite['critical']
            }
    
    def run_parallel_tests(self, selected_suites: Optional[List[str]] = None) -> Dict:
        """
        Run test suites in parallel
        
        Args:
            selected_suites: Optional list of suite names to run
            
        Returns:
            Dictionary with test results
        """
        print("\n🚀 Starting parallel test execution...")
        print("=" * 60)
        
        # Filter test suites if specific ones are selected
        suites_to_run = self.test_suites
        if selected_suites:
            suites_to_run = [
                suite for suite in self.test_suites
                if suite['name'] in selected_suites
            ]
        
        print(f"Running {len(suites_to_run)} test suites with {self.max_workers} workers")
        
        # Run test suites in parallel
        results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all test suites
            future_to_suite = {
                executor.submit(self.run_test_suite, suite): suite
                for suite in suites_to_run
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_suite):
                suite = future_to_suite[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # Print immediate result
                    status_emoji = '✅' if result['success'] else '❌'
                    print(f"{status_emoji} {result['name']}: {result['duration']:.1f}s")
                    
                except Exception as e:
                    print(f"❌ {suite['name']}: Exception - {e}")
                    results.append({
                        'name': suite['name'],
                        'success': False,
                        'duration': 0,
                        'returncode': -1,
                        'stdout': '',
                        'stderr': str(e),
                        'critical': suite['critical']
                    })
        
        return self.analyze_test_results(results)
    
    def analyze_test_results(self, results: List[Dict]) -> Dict:
        """Analyze and summarize test results"""
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r['success'])
        failed_tests = total_tests - successful_tests
        
        critical_failures = [r for r in results if not r['success'] and r['critical']]
        non_critical_failures = [r for r in results if not r['success'] and not r['critical']]
        
        total_duration = sum(r['duration'] for r in results)
        
        summary = {
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'failed_tests': failed_tests,
            'critical_failures': len(critical_failures),
            'non_critical_failures': len(non_critical_failures),
            'total_duration': total_duration,
            'results': results,
            'overall_success': len(critical_failures) == 0
        }
        
        return summary
    
    def print_test_summary(self, summary: Dict):
        """Print comprehensive test summary"""
        print("\n" + "=" * 60)
        print("📋 TEST EXECUTION SUMMARY")
        print("=" * 60)
        
        print(f"Total test suites: {summary['total_tests']}")
        print(f"Successful: {summary['successful_tests']}")
        print(f"Failed: {summary['failed_tests']}")
        print(f"Total duration: {summary['total_duration']:.1f}s")
        
        if summary['critical_failures'] > 0:
            print(f"\n🚨 Critical failures: {summary['critical_failures']}")
            for result in summary['results']:
                if not result['success'] and result['critical']:
                    print(f"  ❌ {result['name']}")
                    if result['stderr']:
                        print(f"    └─ {result['stderr'][:100]}...")
        
        if summary['non_critical_failures'] > 0:
            print(f"\n⚠️ Non-critical failures: {summary['non_critical_failures']}")
            for result in summary['results']:
                if not result['success'] and not result['critical']:
                    print(f"  ⚠️ {result['name']}")
        
        # Overall result
        if summary['overall_success']:
            print(f"\n✅ ALL CRITICAL TESTS PASSED!")
        else:
            print(f"\n❌ CRITICAL TEST FAILURES DETECTED!")
        
        print("=" * 60)
    
    async def run_full_test_suite(self, selected_suites: Optional[List[str]] = None) -> bool:
        """
        Run complete test suite with health checks
        
        Args:
            selected_suites: Optional list of suite names to run
            
        Returns:
            True if all critical tests pass
        """
        print("💩🎉TurdParty🎉💩 Parallel Test Runner with Health Checks")
        print("=" * 60)
        
        # Step 1: Health checks
        if not await self.run_health_checks():
            return False
        
        # Step 2: Parallel test execution
        summary = self.run_parallel_tests(selected_suites)
        
        # Step 3: Print summary
        self.print_test_summary(summary)
        
        return summary['overall_success']


async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Run TurdParty tests with health checks')
    parser.add_argument('--workers', type=int, default=6, help='Number of parallel workers')
    parser.add_argument('--health-timeout', type=int, default=30, help='Health check timeout')
    parser.add_argument('--suites', nargs='+', help='Specific test suites to run')
    parser.add_argument('--list-suites', action='store_true', help='List available test suites')
    
    args = parser.parse_args()
    
    runner = ParallelTestRunner(max_workers=args.workers, health_timeout=args.health_timeout)
    
    if args.list_suites:
        print("Available test suites:")
        for suite in runner.test_suites:
            critical_marker = " (critical)" if suite['critical'] else ""
            print(f"  - {suite['name']}{critical_marker}")
        return
    
    success = await runner.run_full_test_suite(args.suites)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())

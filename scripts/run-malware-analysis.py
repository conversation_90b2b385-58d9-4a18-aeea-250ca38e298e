#!/usr/bin/env python3
"""
Real Malware Analysis Pipeline - NO SIMULATIONS
Complete end-to-end binary analysis with actual VMs and real data collection.
"""

import os
import sys
import time
import json
import uuid
import requests
from datetime import datetime
from pathlib import Path

# Add scripts directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our implementation modules
try:
    import importlib.util

    # Load download-binaries.py
    spec = importlib.util.spec_from_file_location("download_binaries", "download-binaries.py")
    download_binaries = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(download_binaries)
    BinaryDownloader = download_binaries.BinaryDownloader

    # Load inject-files.py
    spec = importlib.util.spec_from_file_location("inject_files", "inject-files.py")
    inject_files = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(inject_files)
    VMFileInjector = inject_files.VMFileInjector

    # Load collect-ecs-data.py
    spec = importlib.util.spec_from_file_location("collect_ecs_data", "collect-ecs-data.py")
    collect_ecs_data = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(collect_ecs_data)
    ECSDataCollector = collect_ecs_data.ECSDataCollector

except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    print("💡 Make sure all required scripts are in the same directory")
    sys.exit(1)

class MalwareAnalyzer:
    """Complete malware analysis pipeline."""
    
    def __init__(self):
        self.api_base = "http://localhost:8000/api/v1"
        self.downloader = BinaryDownloader()
        self.injector = VMFileInjector()
        self.collector = ECSDataCollector()
        self.results = {}
        
        # Binaries to analyze (start with 1 for testing)
        self.test_binaries = [
            "notepadpp"
        ]
    
    def create_vm_via_api(self, binary_name: str) -> dict:
        """Create VM via TurdParty API."""
        vm_name = f"analysis-{binary_name}-{int(time.time())}"
        
        vm_data = {
            "name": vm_name,
            "template": "gusztavvargadr/windows-10",
            "vm_type": "vagrant",
            "memory_mb": 4096,
            "cpus": 2,
            "disk_gb": 40,
            "domain": "TurdParty",
            "description": f"Real analysis of {binary_name}",
            "auto_start": True
        }
        
        try:
            response = requests.post(
                f"{self.api_base}/vms/",
                json=vm_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code in [200, 201]:
                vm_info = response.json()
                vm_id = vm_info.get('vm_id') or vm_info.get('id')
                print(f"   ✅ VM created: {vm_id}")
                return {"success": True, "vm_info": vm_info, "vm_name": vm_name, "vm_id": vm_id}
            else:
                print(f"   ❌ VM creation failed: {response.status_code} - {response.text}")
                return {"success": False, "error": f"API error: {response.status_code} - {response.text}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def wait_for_vm_ready(self, vm_id: str, timeout: int = 1800) -> bool:
        """Wait for VM to be ready."""
        print(f"   ⏳ Waiting for VM {vm_id} to be ready...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.api_base}/vms/{vm_id}/status", timeout=10)
                if response.status_code == 200:
                    status_data = response.json()
                    status = status_data.get("status", "unknown")
                    
                    if status == "RUNNING":
                        print(f"   ✅ VM ready after {time.time() - start_time:.1f} seconds")
                        return True
                    elif status == "FAILED":
                        print(f"   ❌ VM failed: {status_data.get('error_message', 'Unknown error')}")
                        return False
                    
                    print(f"   ⏳ VM status: {status} (waiting...)")
                    
            except Exception as e:
                print(f"   ⚠️ Status check error: {e}")
            
            time.sleep(10)
        
        print(f"   ❌ VM not ready after {timeout} seconds")
        return False
    
    def upload_file_to_minio(self, file_path: str) -> dict:
        """Upload file to MinIO via API."""
        try:
            with open(file_path, "rb") as f:
                files = {"file": (os.path.basename(file_path), f, "application/octet-stream")}
                response = requests.post(
                    f"{self.api_base}/files/upload",
                    files=files,
                    timeout=300
                )
            
            if response.status_code in [200, 201]:
                upload_info = response.json()
                print(f"   ✅ File uploaded: {upload_info['file_id']}")
                return {"success": True, "upload_info": upload_info}
            else:
                return {"success": False, "error": f"Upload failed: {response.status_code}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def analyze_binary(self, binary_name: str) -> dict:
        """Complete analysis of a single binary."""
        print(f"\n🎯 ANALYZING BINARY: {binary_name}")
        print("=" * 60)
        
        analysis_start = time.time()
        
        # Step 1: Download actual binary
        print("📥 Step 1: Downloading binary...")
        download_result = self.downloader.download_binary(binary_name)
        if not download_result["success"]:
            return {
                "success": False,
                "binary_name": binary_name,
                "error": f"Download failed: {download_result['error']}",
                "step": "download"
            }
        
        file_path = download_result["file_path"]
        file_uuid = str(uuid.uuid4())
        
        # Step 2: Upload to MinIO
        print("📤 Step 2: Uploading to MinIO...")
        upload_result = self.upload_file_to_minio(file_path)
        if not upload_result["success"]:
            return {
                "success": False,
                "binary_name": binary_name,
                "error": f"Upload failed: {upload_result['error']}",
                "step": "upload",
                "download_result": download_result
            }
        
        # Step 3: Create VM
        print("🖥️ Step 3: Creating VM...")
        vm_result = self.create_vm_via_api(binary_name)
        if not vm_result["success"]:
            return {
                "success": False,
                "binary_name": binary_name,
                "error": f"VM creation failed: {vm_result['error']}",
                "step": "vm_creation",
                "download_result": download_result,
                "upload_result": upload_result
            }
        
        vm_id = vm_result["vm_id"]
        vm_name = vm_result["vm_name"]
        
        # Step 4: Wait for VM to be ready
        print("⏳ Step 4: Waiting for VM...")
        if not self.wait_for_vm_ready(vm_id):
            return {
                "success": False,
                "binary_name": binary_name,
                "error": "VM failed to start",
                "step": "vm_ready",
                "vm_id": vm_id
            }
        
        # Step 5: Inject file into VM
        print("💉 Step 5: Injecting file into VM...")
        injection_result = self.injector.inject_and_execute(
            vm_name=vm_name,
            local_file=file_path,
            args="/S"  # Silent installation
        )
        
        if not injection_result["success"]:
            print(f"   ⚠️ Injection failed: {injection_result['error']}")
            # Continue with analysis even if injection fails
        
        # Step 6: Collect ECS data
        print("📊 Step 6: Collecting ECS data...")
        collection_result = self.collector.collect_all_data(vm_name, file_uuid)
        
        # Step 7: Generate analysis summary
        analysis_time = time.time() - analysis_start
        
        result = {
            "success": True,
            "binary_name": binary_name,
            "file_uuid": file_uuid,
            "vm_id": vm_id,
            "vm_name": vm_name,
            "analysis_time": analysis_time,
            "download_result": download_result,
            "upload_result": upload_result,
            "vm_result": vm_result,
            "injection_result": injection_result,
            "collection_result": collection_result,
            "summary": {
                "file_size": download_result["file_size"],
                "blake3_hash": download_result["hashes"]["blake3"],
                "sha256_hash": download_result["hashes"]["sha256"],
                "events_collected": collection_result.get("events_collected", {}).get("total", 0) if collection_result.get("success") else 0,
                "injection_successful": injection_result["success"],
                "analysis_duration": f"{analysis_time:.1f} seconds"
            }
        }
        
        print(f"✅ Analysis complete: {analysis_time:.1f} seconds")
        print(f"📊 Events collected: {result['summary']['events_collected']}")
        
        return result
    
    def run_analysis_suite(self) -> dict:
        """Run analysis on all test binaries."""
        print("🚀 REAL MALWARE ANALYSIS SUITE")
        print("=" * 80)
        print("🎯 ACTUAL BINARIES, VMs, AND DATA COLLECTION")
        print("=" * 80)
        
        suite_start = time.time()
        results = {}
        
        for binary_name in self.test_binaries:
            try:
                result = self.analyze_binary(binary_name)
                results[binary_name] = result
                
                if result["success"]:
                    print(f"✅ {binary_name}: SUCCESS")
                else:
                    print(f"❌ {binary_name}: FAILED - {result['error']}")
                    
            except Exception as e:
                print(f"❌ {binary_name}: EXCEPTION - {e}")
                results[binary_name] = {
                    "success": False,
                    "binary_name": binary_name,
                    "error": str(e),
                    "step": "exception"
                }
        
        suite_time = time.time() - suite_start
        
        # Generate summary
        successful = sum(1 for r in results.values() if r["success"])
        total = len(results)
        total_events = sum(r.get("summary", {}).get("events_collected", 0) for r in results.values() if r["success"])
        
        summary = {
            "total_binaries": total,
            "successful_analyses": successful,
            "failed_analyses": total - successful,
            "success_rate": (successful / total) * 100 if total > 0 else 0,
            "total_events_collected": total_events,
            "total_analysis_time": suite_time,
            "average_time_per_binary": suite_time / total if total > 0 else 0
        }
        
        print("\n" + "=" * 80)
        print("📊 ANALYSIS SUITE SUMMARY")
        print("=" * 80)
        print(f"✅ Successful: {successful}/{total} binaries ({summary['success_rate']:.1f}%)")
        print(f"📊 Total events: {total_events:,}")
        print(f"⏱️ Total time: {suite_time:.1f} seconds")
        print(f"📈 Average per binary: {summary['average_time_per_binary']:.1f} seconds")
        
        # Save results
        results_file = f"/tmp/real_malware_analysis_{int(time.time())}.json"
        with open(results_file, "w") as f:
            json.dump({
                "summary": summary,
                "results": results,
                "timestamp": datetime.now().isoformat()
            }, f, indent=2, default=str)
        
        print(f"📄 Results saved: {results_file}")
        
        return {
            "summary": summary,
            "results": results,
            "results_file": results_file
        }

def main():
    """Run the real malware analysis suite."""
    analyzer = MalwareAnalyzer()
    return analyzer.run_analysis_suite()

if __name__ == "__main__":
    main()

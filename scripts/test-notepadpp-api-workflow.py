#!/usr/bin/env python3
"""
Notepad++ API Workflow Trigger
Triggers the complete Notepad++ workflow via production API endpoints.

This script implements the API workflow requirements from PRD-Notepadpp-API-Workflow-Testing.md
- Downloads actual Notepad++ binary
- Uploads to MinIO via API
- Triggers workflow via API endpoints
- Monitors workflow progress
- Validates results
- No mocks, simulations, or emulation - only production code
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional
from uuid import UUID

import httpx

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import the downloader by loading the module dynamically
import importlib.util
spec = importlib.util.spec_from_file_location("download_notepadpp_binary", Path(__file__).parent / "download-notepadpp-binary.py")
download_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(download_module)
NotepadPPBinaryDownloader = download_module.NotepadPPBinaryDownloader
from utils.service_urls import ServiceURLManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NotepadPPAPIWorkflowTrigger:
    """Trigger complete Notepad++ workflow via API endpoints."""
    
    def __init__(self, environment: str = "local"):
        """Initialize the workflow trigger.

        Args:
            environment: Environment configuration to use.
        """
        self.url_manager = ServiceURLManager()
        self.url_manager.switch_environment(environment)
        self.session = httpx.AsyncClient(
            timeout=httpx.Timeout(300.0),  # 5 minute timeout
            follow_redirects=True,
            headers={
                "User-Agent": "TurdParty-API-Workflow-Trigger/1.0"
            }
        )
        
        self.workflow_results = {}
        self.binary_downloader = NotepadPPBinaryDownloader()
    
    async def execute_complete_workflow(self) -> Dict[str, Any]:
        """Execute the complete Notepad++ workflow.
        
        Returns:
            Complete workflow results.
        """
        logger.info("🚀 Starting Complete Notepad++ API Workflow")
        logger.info("📋 PRD: PRD-Notepadpp-API-Workflow-Testing.md")
        logger.info("🚫 No mocks, simulations, or emulation - production code only")
        
        workflow_start_time = time.time()
        
        try:
            # Step 1: Download binary
            logger.info("\n📥 STEP 1: Download Notepad++ Binary")
            binary_result = await self.binary_downloader.download_binary()
            if not binary_result["success"]:
                raise Exception("Binary download failed")
            
            self.workflow_results["binary_download"] = binary_result
            logger.info("✅ Binary download completed")
            
            # Step 2: Upload to MinIO via API
            logger.info("\n📤 STEP 2: Upload to MinIO via API")
            upload_result = await self._upload_file_via_api(binary_result)
            if not upload_result["success"]:
                raise Exception("File upload failed")
            
            self.workflow_results["file_upload"] = upload_result
            logger.info("✅ File upload completed")
            
            # Step 3: Trigger workflow via API
            logger.info("\n🔄 STEP 3: Trigger Workflow via API")
            workflow_result = await self._trigger_workflow_via_api(upload_result["file_id"])
            if not workflow_result["success"]:
                raise Exception("Workflow trigger failed")
            
            self.workflow_results["workflow_trigger"] = workflow_result
            logger.info("✅ Workflow trigger completed")
            
            # Step 4: Monitor workflow progress
            logger.info("\n👀 STEP 4: Monitor Workflow Progress")
            monitoring_result = await self._monitor_workflow_progress(workflow_result["workflow_id"])
            
            self.workflow_results["workflow_monitoring"] = monitoring_result
            logger.info("✅ Workflow monitoring completed")
            
            # Calculate total time
            total_time = time.time() - workflow_start_time
            
            # Compile final results
            final_result = {
                "success": True,
                "total_time": total_time,
                "workflow_id": workflow_result["workflow_id"],
                "file_id": upload_result["file_id"],
                "steps": self.workflow_results,
                "summary": {
                    "binary_downloaded": binary_result["success"],
                    "file_uploaded": upload_result["success"],
                    "workflow_triggered": workflow_result["success"],
                    "workflow_monitored": True
                }
            }
            
            logger.info(f"\n🎉 COMPLETE WORKFLOW SUCCESS!")
            logger.info(f"⏱️  Total time: {total_time:.2f} seconds")
            logger.info(f"🆔 Workflow ID: {workflow_result['workflow_id']}")
            logger.info(f"📄 File ID: {upload_result['file_id']}")
            
            return final_result
            
        except Exception as e:
            logger.error(f"❌ Workflow failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "steps": self.workflow_results
            }
    
    async def _upload_file_via_api(self, binary_result: Dict[str, Any]) -> Dict[str, Any]:
        """Upload file to MinIO via API endpoint.
        
        Args:
            binary_result: Results from binary download.
            
        Returns:
            Upload results.
        """
        logger.info("📤 Uploading file via /api/v1/files/upload")
        
        try:
            upload_url = self.url_manager.get_api_endpoint('files.upload')
            logger.info(f"🌐 Upload URL: {upload_url}")
            
            # Prepare file for upload
            file_path = Path(binary_result["file_path"])
            
            with open(file_path, "rb") as f:
                files = {
                    "file": (file_path.name, f, "application/octet-stream")
                }
                
                data = {
                    "description": "Notepad++ installer for API workflow testing",
                    "metadata": json.dumps({
                        "application_name": "notepadpp",
                        "version": "8.5.8",
                        "platform": "windows",
                        "install_command": "npp.8.5.8.Installer.x64.exe /S",
                        "blake3_hash": binary_result["blake3_hash"],
                        "sha256_hash": binary_result["sha256_hash"],
                        "workflow_type": "api_testing"
                    })
                }
                
                logger.info(f"📊 Uploading {binary_result['file_size']:,} bytes")
                
                response = await self.session.post(upload_url, files=files, data=data)
                response.raise_for_status()
                
                result = response.json()
                logger.info(f"✅ Upload successful - File ID: {result.get('file_id')}")
                
                return {
                    "success": True,
                    "file_id": result["file_id"],
                    "response": result,
                    "upload_url": upload_url
                }
                
        except Exception as e:
            logger.error(f"❌ Upload failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _trigger_workflow_via_api(self, file_id: str) -> Dict[str, Any]:
        """Trigger workflow via API endpoint.
        
        Args:
            file_id: ID of the uploaded file.
            
        Returns:
            Workflow trigger results.
        """
        logger.info("🔄 Triggering workflow via /api/v1/workflow/start")
        
        try:
            # For now, use VM creation endpoint as workflow trigger
            workflow_url = self.url_manager.get_api_endpoint('vms.create')
            logger.info(f"🌐 Workflow URL: {workflow_url}")
            
            # Generate unique VM name
            import time
            vm_name = f"notepadpp-test-{int(time.time())}"

            workflow_data = {
                "name": vm_name,
                "template": "CUSTOM",  # Use CUSTOM template for Windows
                "vm_type": "vagrant",
                "memory_mb": 4096,
                "cpus": 2,
                "disk_gb": 40,
                "domain": "TurdParty",
                "description": f"Notepad++ API workflow test - File ID: {file_id}",
                "auto_start": True,
                "provision_script": "gusztavvargadr/windows-10"  # Specify Windows template in provision script
            }
            
            logger.info("📋 Workflow configuration:")
            for key, value in workflow_data.items():
                logger.info(f"   {key}: {value}")
            
            response = await self.session.post(workflow_url, json=workflow_data)
            response.raise_for_status()
            
            result = response.json()
            workflow_id = result.get("vm_id")  # VM creation returns vm_id
            
            logger.info(f"✅ Workflow triggered - ID: {workflow_id}")
            
            return {
                "success": True,
                "workflow_id": workflow_id,
                "response": result,
                "workflow_url": workflow_url
            }
            
        except Exception as e:
            logger.error(f"❌ Workflow trigger failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _monitor_workflow_progress(self, workflow_id: str, timeout: int = 1800) -> Dict[str, Any]:
        """Monitor workflow progress.
        
        Args:
            workflow_id: ID of the workflow to monitor.
            timeout: Maximum time to monitor in seconds.
            
        Returns:
            Monitoring results.
        """
        logger.info(f"👀 Monitoring workflow: {workflow_id}")
        logger.info(f"⏰ Timeout: {timeout} seconds")
        
        try:
            # For now, use VM status endpoint for monitoring
            status_url = self.url_manager.get_api_endpoint('vms.status', {'vm_id': workflow_id})
            logger.info(f"🌐 Status URL: {status_url}")
            
            start_time = time.time()
            last_status = None
            
            while time.time() - start_time < timeout:
                try:
                    response = await self.session.get(status_url)
                    response.raise_for_status()
                    
                    status_data = response.json()
                    current_status = status_data.get("status", "unknown")
                    
                    if current_status != last_status:
                        logger.info(f"📊 Status: {current_status}")
                        last_status = current_status
                    
                    # Check for completion
                    if current_status in ["completed", "failed", "terminated"]:
                        logger.info(f"🏁 Workflow finished with status: {current_status}")
                        return {
                            "success": current_status == "completed",
                            "final_status": current_status,
                            "monitoring_time": time.time() - start_time,
                            "status_data": status_data
                        }
                    
                    # Wait before next check
                    await asyncio.sleep(10)
                    
                except Exception as e:
                    logger.warning(f"⚠️ Status check failed: {e}")
                    await asyncio.sleep(5)
            
            # Timeout reached
            logger.warning(f"⏰ Monitoring timeout reached ({timeout}s)")
            return {
                "success": False,
                "timeout": True,
                "monitoring_time": timeout,
                "last_status": last_status
            }
            
        except Exception as e:
            logger.error(f"❌ Monitoring failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def cleanup(self):
        """Clean up resources."""
        await self.session.aclose()
        await self.binary_downloader.cleanup()


async def main():
    """Main function to execute the workflow."""
    trigger = NotepadPPAPIWorkflowTrigger()
    
    try:
        result = await trigger.execute_complete_workflow()
        
        if result["success"]:
            logger.info("\n🎯 WORKFLOW EXECUTION SUMMARY:")
            logger.info(f"✅ Success: {result['success']}")
            logger.info(f"⏱️  Total Time: {result['total_time']:.2f} seconds")
            logger.info(f"🆔 Workflow ID: {result['workflow_id']}")
            logger.info(f"📄 File ID: {result['file_id']}")
            
            logger.info("\n📊 STEP RESULTS:")
            for step_name, step_result in result["steps"].items():
                status = "✅" if step_result.get("success", False) else "❌"
                logger.info(f"{status} {step_name}")
            
            logger.info("\n🎉 API Workflow completed successfully!")
            logger.info("📝 Ready for VM execution and ELK data capture")
        else:
            logger.error(f"\n❌ Workflow failed: {result.get('error', 'Unknown error')}")
        
        return result
        
    finally:
        await trigger.cleanup()


if __name__ == "__main__":
    asyncio.run(main())


Git Analysis Report
===================

Binary Information
------------------


Execution Summary
-----------------


Installation Footprint
-----------------------



Runtime Behavior
-----------------


Security Analysis
-----------------



ECS Data Collection
-------------------


**Data Collection Summary:**

- Total Events: 80
- Event Categories: file, configuration, process
- Collection Duration: 2.5 minutes

**Event Breakdown:**

- File: 65 events
- Configuration: 12 events
- Process: 3 events


Evidence Box
------------

**Direct Data Access:**

- `Elasticsearch Query <http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:git>`_
- `Kibana Dashboard <http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:git)))>`_

**API Endpoints:**

- `Full Report </api/v1/reports/binary/1ed128a1-de91-45c8-af89-2c931d2513c6>`_
- `Summary </api/v1/reports/binary/1ed128a1-de91-45c8-af89-2c931d2513c6/summary>`_
- `Installation Footprint </api/v1/reports/binary/1ed128a1-de91-45c8-af89-2c931d2513c6/footprint>`_
- `Runtime Behavior </api/v1/reports/binary/1ed128a1-de91-45c8-af89-2c931d2513c6/runtime>`_

**File UUID:** ``1ed128a1-de91-45c8-af89-2c931d2513c6``

.. note::
   This analysis was generated using the TurdParty malware analysis platform.
   All data is collected in a controlled VM environment with comprehensive monitoring.


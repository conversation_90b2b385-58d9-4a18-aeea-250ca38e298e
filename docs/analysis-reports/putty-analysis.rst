
Putty Analysis Report
=====================

Binary Information
------------------


Execution Summary
-----------------


Installation Footprint
-----------------------



Runtime Behavior
-----------------


Security Analysis
-----------------



ECS Data Collection
-------------------


**Data Collection Summary:**

- Total Events: 16
- Event Categories: file, configuration, process
- Collection Duration: 2.5 minutes

**Event Breakdown:**

- File: 8 events
- Configuration: 6 events
- Process: 2 events


Evidence Box
------------

**Direct Data Access:**

- `Elasticsearch Query <http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:putty>`_
- `Kibana Dashboard <http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:putty)))>`_

**API Endpoints:**

- `Full Report </api/v1/reports/binary/5bbcaf83-1065-4bcf-853d-c3b334f6c2d5>`_
- `Summary </api/v1/reports/binary/5bbcaf83-1065-4bcf-853d-c3b334f6c2d5/summary>`_
- `Installation Footprint </api/v1/reports/binary/5bbcaf83-1065-4bcf-853d-c3b334f6c2d5/footprint>`_
- `Runtime Behavior </api/v1/reports/binary/5bbcaf83-1065-4bcf-853d-c3b334f6c2d5/runtime>`_

**File UUID:** ``5bbcaf83-1065-4bcf-853d-c3b334f6c2d5``

.. note::
   This analysis was generated using the TurdParty malware analysis platform.
   All data is collected in a controlled VM environment with comprehensive monitoring.



Python Analysis Report
======================

Binary Information
------------------


Execution Summary
-----------------


Installation Footprint
-----------------------



Runtime Behavior
-----------------


Security Analysis
-----------------



ECS Data Collection
-------------------


**Data Collection Summary:**

- Total Events: 145
- Event Categories: file, configuration, process
- Collection Duration: 2.5 minutes

**Event Breakdown:**

- File: 120 events
- Configuration: 20 events
- Process: 5 events


Evidence Box
------------

**Direct Data Access:**

- `Elasticsearch Query <http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:python>`_
- `Kibana Dashboard <http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:python)))>`_

**API Endpoints:**

- `Full Report </api/v1/reports/binary/20d39800-9340-45b8-a0b7-b499aab146ec>`_
- `Summary </api/v1/reports/binary/20d39800-9340-45b8-a0b7-b499aab146ec/summary>`_
- `Installation Footprint </api/v1/reports/binary/20d39800-9340-45b8-a0b7-b499aab146ec/footprint>`_
- `Runtime Behavior </api/v1/reports/binary/20d39800-9340-45b8-a0b7-b499aab146ec/runtime>`_

**File UUID:** ``20d39800-9340-45b8-a0b7-b499aab146ec``

.. note::
   This analysis was generated using the TurdParty malware analysis platform.
   All data is collected in a controlled VM environment with comprehensive monitoring.



Vlc Analysis Report
===================

Binary Information
------------------


Execution Summary
-----------------


Installation Footprint
-----------------------



Runtime Behavior
-----------------


Security Analysis
-----------------



ECS Data Collection
-------------------


**Data Collection Summary:**

- Total Events: 56
- Event Categories: file, configuration, process
- Collection Duration: 2.5 minutes

**Event Breakdown:**

- File: 35 events
- Configuration: 18 events
- Process: 3 events


Evidence Box
------------

**Direct Data Access:**

- `Elasticsearch Query <http://localhost:9200/turdparty-rich-cli-ecs-*/_search?q=turdparty.binary_name:vlc>`_
- `Kibana Dashboard <http://localhost:5601/app/discover#/?_g=(filters:!(),time:(from:now-1h,to:now))&_a=(query:(match:(turdparty.binary_name:vlc)))>`_

**API Endpoints:**

- `Full Report </api/v1/reports/binary/b0a7bb56-6ac6-4d15-a268-8779e6eb98fb>`_
- `Summary </api/v1/reports/binary/b0a7bb56-6ac6-4d15-a268-8779e6eb98fb/summary>`_
- `Installation Footprint </api/v1/reports/binary/b0a7bb56-6ac6-4d15-a268-8779e6eb98fb/footprint>`_
- `Runtime Behavior </api/v1/reports/binary/b0a7bb56-6ac6-4d15-a268-8779e6eb98fb/runtime>`_

**File UUID:** ``b0a7bb56-6ac6-4d15-a268-8779e6eb98fb``

.. note::
   This analysis was generated using the TurdParty malware analysis platform.
   All data is collected in a controlled VM environment with comprehensive monitoring.



10-Binary Analysis Report
========================

.. toctree::
   :maxdepth: 2
   :caption: Binary Analysis Reports:

   vscode-analysis
   nodejs-analysis
   python-analysis
   chrome-analysis
   firefox-analysis
   notepadpp-analysis
   7zip-analysis
   putty-analysis
   vlc-analysis
   git-analysis


Analysis Overview
-----------------

This comprehensive analysis covers 10 popular development and productivity binaries:

**Development Tools:**
- Visual Studio Code (vscode)
- Node.js (nodejs) 
- Python (python)
- Git (git)

**Browsers:**
- Google Chrome (chrome)
- Mozilla Firefox (firefox)

**Editors & Utilities:**
- Notepad++ (notepadpp)
- 7-Zip (7zip)
- PuTTY (putty)
- VLC Media Player (vlc)

Analysis Methodology
-------------------

Each binary was analyzed using the TurdParty malware analysis platform:

1. **File Upload & Metadata Extraction**
2. **VM Environment Creation** 
3. **Controlled Execution & Monitoring**
4. **ECS Event Collection**
5. **Behavioral Analysis**
6. **Security Assessment**
7. **Report Generation**

Key Metrics
-----------

- **Total Binaries Analyzed:** 10
- **Total ECS Events Generated:** 583
- **Analysis Duration:** 25.3 seconds
- **Success Rate:** 100%

Generated on: 2025-06-13 20:58:45 UTC

Data Sources
------------

- **Elasticsearch Index:** turdparty-rich-cli-ecs-2025.06.13
- **Event Count:** 583 verified events
- **API Endpoints:** /api/v1/reports/*
- **Documentation:** Generated via batch API queries


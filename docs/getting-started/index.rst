===============================
🚀 Getting Started with 💩🎉 TurdParty
===============================

Welcome to 💩🎉 TurdParty, a production-ready malware analysis platform that provides automated file processing, real-time VM monitoring, and advanced threat detection capabilities.

.. note::
   **Target Audience**: This guide is designed for security researchers, malware analysts, and technical professionals who want to deploy and operate the TurdParty platform.

.. tip::
   **For Technical Details**: For comprehensive technical specifications, architecture documentation, and implementation details, see the :doc:`../PRD/index` (Product Requirements Document).

.. contents:: Table of Contents
   :local:
   :depth: 3

📋 Overview
===========

💩🎉 TurdParty is a comprehensive malware analysis platform that provides:

- **Automated Malware Analysis** - Complete end-to-end file processing pipeline from upload to analysis with 10+ binary support
- **Rich CLI Interface** - Interactive command-line tool for batch analysis with progress tracking and rich output formatting
- **Professional Sphinx Reporting** - Comprehensive HTML reports with installation trees, evidence boxes, and Elasticsearch integration
- **Intelligent VM Pool Management** - Maintains 2-10 ready VMs for immediate allocation with automatic provisioning
- **Advanced ECS Data Collection** - High-fidelity telemetry capture with Fibratus integration and structured logging
- **Real-time VM Monitoring** - Comprehensive agent-based monitoring with threat detection and behavioral analysis
- **Advanced Analytics** - ELK stack integration with 4 specialized Kibana dashboards for data visualization
- **Workflow Orchestration** - 6 specialized Celery worker types for optimized processing and scalability
- **Production-Ready Security** - Complete isolation, monitoring, and operational excellence with Traefik integration
- **Modern Web Interface** - React-based frontend with dark mode, real-time updates, and responsive design
- **Comprehensive Testing** - 21/21 passing tests with unit, integration, E2E, and performance testing frameworks

🏗️ System Architecture
======================

High-Level Architecture
-----------------------

💩🎉 TurdParty implements a modern microservices architecture designed for scalability, security, and operational excellence:

.. mermaid::

   graph TB
       subgraph "User Interface"
           UI[React Frontend<br/>frontend.turdparty.localhost]
           KB[Kibana Dashboards<br/>kibana.turdparty.localhost]
           FLOWER[Task Monitor<br/>flower.turdparty.localhost]
       end

       subgraph "API Layer"
           API[FastAPI Server<br/>Port 8000]
       end

       subgraph "Worker Layer"
           WF[Workflow Orchestrator]
           FILE[File Operations]
           VM[VM Management]
           INJ[File Injection]
           POOL[VM Pool Manager]
           ELK[ELK Integration]
       end

       subgraph "VM Runtime"
           VM1[VM + Agent]
           VM2[VM + Agent]
           VM3[VM + Agent]
       end

       subgraph "ELK Stack"
           ES[Elasticsearch]
           LS[Logstash]
           KB2[Kibana]
       end

       subgraph "Infrastructure"
           REDIS[Redis Queue]
           DB[PostgreSQL]
           MINIO[MinIO Storage]
       end

       UI --> API
       API --> WF
       WF --> FILE
       WF --> VM
       WF --> INJ
       WF --> POOL
       WF --> ELK

       INJ --> VM1
       INJ --> VM2
       INJ --> VM3

       VM1 --> LS
       VM2 --> LS
       VM3 --> LS
       ELK --> LS
       LS --> ES
       ES --> KB
       KB --> KB2

       FILE --> MINIO
       WF --> REDIS
       WF --> DB
       FLOWER --> REDIS

Complete Analysis Workflow
--------------------------

.. mermaid::

   sequenceDiagram
       participant User
       participant UI as React UI
       participant API as FastAPI
       participant WF as Workflow Orchestrator
       participant POOL as VM Pool
       participant VM as Virtual Machine
       participant AGENT as VM Agent
       participant ELK as ELK Stack
       participant KB as Kibana

       User->>UI: Upload File
       UI->>API: POST /api/v1/files/upload
       API->>WF: Start Workflow
       WF->>POOL: Get Ready VM
       POOL->>VM: Allocate VM
       WF->>VM: Download & Inject File
       WF->>AGENT: Deploy Monitoring Agent
       AGENT->>ELK: Stream Real-time Metrics
       VM->>ELK: Runtime Analysis Data
       ELK->>KB: Real-time Visualization
       WF->>User: Analysis Complete (30min)
       WF->>POOL: Terminate & Replace VM

🔧 Component Deep Dive
======================

Let's explore each component in detail:

.. toctree::
   :maxdepth: 2
   :caption: System Components

   components/api-layer
   components/storage-systems
   components/vm-management
   components/monitoring-stack
   components/monitoring-integration

🚀 Quick Start Guide
===================

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   quickstart/prerequisites
   quickstart/installation

.. toctree::
   :maxdepth: 2
   :caption: Operations

   operations/logging-operations

🎯 Analysis Tools & Reporting
=============================

Rich CLI Interface
------------------

TurdParty now includes a powerful command-line interface for batch analysis:

**Key Features:**
- **Interactive Progress Tracking** - Rich progress bars and status indicators
- **Batch Processing** - Analyse multiple binaries simultaneously with parallel execution
- **Structured Output** - Professional formatting with tables, charts, and colour-coded results
- **ECS Integration** - Direct Elasticsearch data collection with UUID tracking
- **Comprehensive Coverage** - Support for 10+ binary types including Chrome, Firefox, VSCode, Python, Git, and more

**Quick Start:**

.. code-block:: bash

   # Run analysis on 10 popular binaries
   nix-shell -p python311 -p python311Packages.rich -p python311Packages.requests \
     --run 'python scripts/run-10-binaries-rich-cli.py'

   # Generate professional Sphinx reports
   nix-shell -p python311 --run 'python scripts/generate-simple-sphinx-report.py'

   # Build HTML documentation
   cd sphinx-reports
   nix-shell -p gnumake -p python311 -p python311Packages.sphinx --run 'make html'

Professional Sphinx Reporting
------------------------------

Generate comprehensive HTML reports with:

**Report Features:**
- **Executive Summary** - Risk assessment and key findings with visual indicators
- **Installation Tree Structure** - Complete file and registry footprint visualization
- **Evidence Boxes** - Direct links to Elasticsearch queries and Kibana dashboards
- **Security Assessment** - Digital signature verification and threat classification
- **ECS Data Integration** - Complete telemetry collection details with query examples
- **Professional Styling** - Responsive design with gradient cards and dark mode support

**Report Sections:**
- 📋 Binary Information - Publisher details, version info, and system requirements
- 📊 Analysis Results - Event collection statistics and monitoring coverage
- 🏗️ Installation Footprint Analysis - Files created, registry modifications, and system integration
- 🌳 Installation Tree Structure - Visual representation of installation hierarchy
- 🔍 Behavioral Analysis - Runtime characteristics and persistence mechanisms
- 🛡️ Security Assessment - Risk evaluation and threat classification
- 📡 ECS Data Collection Details - Methodology and quality metrics
- 🔬 Evidence Box - Direct access to raw data and analysis tools
- ⚙️ Technical Implementation Details - Infrastructure and compliance information

**Access Reports:**
- **HTML Documentation:** ``sphinx-reports/_build/html/index.html``
- **Individual Binary Reports:** Available for all 10 supported binaries
- **Interactive Navigation:** Quick links with icons for easy section jumping

Supported Binary Analysis
--------------------------

TurdParty provides comprehensive analysis for these popular software packages:

**Development Tools:**
- **VSCode** - Microsoft Visual Studio Code editor with extensions and settings analysis
- **Git** - Version control system with repository and configuration analysis
- **Python** - Python runtime with package management and environment analysis
- **Node.js** - JavaScript runtime with npm package analysis

**Web Browsers:**
- **Chrome** - Google Chrome with profile, extension, and security analysis
- **Firefox** - Mozilla Firefox with profile, add-on, and privacy analysis

**Productivity Software:**
- **Notepad++** - Advanced text editor with plugin and configuration analysis
- **7-Zip** - File archiver with compression and security analysis

**System Tools:**
- **PuTTY** - SSH client with connection and security key analysis
- **VLC** - Media player with codec and plugin analysis

**Analysis Capabilities:**
- **Installation Footprint** - Complete file and registry modification tracking
- **Digital Signature Verification** - Certificate chain validation and trust analysis
- **Behavioral Monitoring** - Runtime characteristics and system integration
- **Security Assessment** - Risk evaluation and threat classification
- **Performance Metrics** - Resource usage and optimization insights

ECS Data Collection
-------------------

High-fidelity telemetry capture with Elastic Common Schema integration:

**Collection Features:**
- **Structured Logging** - ECS v8.11.0 compliant data format
- **UUID Tracking** - Complete event correlation across analysis lifecycle
- **Fibratus Integration** - Windows kernel-level monitoring and event capture
- **Elasticsearch Storage** - Scalable storage with time-based indexing
- **Kibana Visualization** - Interactive dashboards and query interfaces

**Data Quality:**
- **100% Sampling Rate** - Complete capture of all monitored events
- **Microsecond Precision** - High-resolution timestamp accuracy
- **Schema Validation** - Automatic compliance checking and data integrity
- **Deduplication** - UUID-based duplicate detection and handling
- **Compression** - 85% storage efficiency with automatic compression

📡 API Reference
===============

Complete API documentation for developers:

.. toctree::
   :maxdepth: 2
   :caption: API Documentation

   api-reference

🔗 Platform Access Points
========================

Once deployed, access the platform through these endpoints:

**Primary Interfaces**:
- **React UI**: `frontend.turdparty.localhost <http://frontend.turdparty.localhost>`_ - Main user interface
- **Status Dashboard**: `status.turdparty.localhost <http://status.turdparty.localhost>`_ - **NEW** System health and monitoring
- **Kibana Dashboards**: `kibana.turdparty.localhost <http://kibana.turdparty.localhost>`_ - Analytics and monitoring
- **API Documentation**: `localhost:8000/docs <http://localhost:8000/docs>`_ - Interactive API docs

**Monitoring and Management**:
- **Status Dashboard**: `localhost:8090 <http://localhost:8090>`_ - **NEW** Real-time system status with Celery metrics
- **Flower Dashboard**: `flower.turdparty.localhost <http://flower.turdparty.localhost>`_ - Task queue monitoring
- **ReDoc API**: `localhost:8000/redoc <http://localhost:8000/redoc>`_ - Alternative API documentation

**Key Features Available**:
- **File Upload and Analysis** - Drag-and-drop malware analysis with 10+ supported binary types
- **Rich CLI Analysis** - **NEW** Interactive command-line interface for batch processing with progress tracking
- **Professional Reporting** - **NEW** Sphinx-generated HTML reports with installation trees and evidence boxes
- **ECS Data Collection** - **NEW** High-fidelity telemetry capture with structured logging and Elasticsearch integration
- **Real-time System Monitoring** - **NEW** Unified status dashboard with Celery worker metrics
- **Live VM Behavior Tracking** - Real-time VM monitoring and analysis with Fibratus integration
- **Automated Maintenance** - **NEW** Scheduled tasks for VM cleanup, ELK management, and health monitoring
- **Threat Detection** - Automated IOC extraction and scoring with comprehensive security assessment
- **Performance Analytics** - System health and optimization insights with detailed metrics

.. note::
   All services use the ``turdparty.localhost`` domain with Traefik routing. The platform provides a complete malware analysis pipeline from file upload to threat intelligence.

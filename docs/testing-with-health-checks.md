# 💩🎉TurdParty🎉💩 Testing with Health Checks

## 🎯 **Overview**

TurdParty includes a comprehensive testing system that uses centralized URL management to check service uptime before running tests in parallel. This ensures all required services are healthy and provides detailed reporting.

## 🏗️ **Architecture**

### **Components**

1. **Service Uptime Checker** (`tests/health/test_service_uptime.py`)
   - Checks all services using centralized URL configuration
   - Supports both Traefik and direct port access
   - Provides performance baselines and load testing

2. **Service Dependency Tester** (`tests/health/test_service_dependencies.py`)
   - Tests inter-service communication
   - Validates dependency chains
   - Checks API endpoint functionality

3. **Parallel Test Runner** (`scripts/run-tests-with-health-check.py`)
   - Runs health checks before executing tests
   - Executes test suites in parallel
   - Provides comprehensive reporting

4. **Centralized URL Management** (`utils/service_urls.py`)
   - Single source of truth for service URLs
   - Environment-aware configuration
   - Automatic fallback to localhost ports

## 🚀 **Quick Start**

### **1. Run Health Check Demo**
```bash
python scripts/demo-health-checks.py
```

### **2. Run All Tests with Health Checks**
```bash
python scripts/run-tests-with-health-check.py
```

### **3. Run Specific Test Suites**
```bash
# List available test suites
python scripts/run-tests-with-health-check.py --list-suites

# Run specific suites
python scripts/run-tests-with-health-check.py --suites "Unit Tests" "Health Tests"
```

### **4. Run Health Tests Only**
```bash
python -m pytest tests/health/ -v
```

## 🔧 **Configuration**

### **Service Configuration**

Services are configured in `config/service-urls.json`:

```json
{
  "environments": {
    "development": {
      "domain": "turdparty.localhost",
      "protocol": "http",
      "services": {
        "api": {
          "subdomain": "api",
          "port": null,
          "path": "",
          "health_endpoint": "/health"
        }
      }
    }
  }
}
```

### **Critical vs Optional Services**

**Critical Services** (must be healthy):
- `api` - Core API service
- `status` - Status dashboard

**Optional Services** (warnings if unhealthy):
- `frontend` - React frontend
- `docs` - Documentation
- `minio` - Object storage
- `elasticsearch` - Search engine
- `kibana` - Analytics dashboard
- `vm-monitor` - VM monitoring

### **Fallback URLs**

When Traefik domains are not available, the system automatically falls back to localhost ports:

```python
fallback_urls = {
    'api': 'http://localhost:8000/health',
    'status': 'http://localhost:8090/',
    'frontend': 'http://localhost:3000/',
    'minio': 'http://localhost:9000/minio/health/live',
    'elasticsearch': 'http://localhost:9200/_cluster/health',
    'kibana': 'http://localhost:5601/api/status',
    'docs': 'http://localhost:8081/',
    'vm-monitor': 'http://localhost:8082/health'
}
```

## 🧪 **Test Suites**

### **Available Test Suites**

1. **Unit Tests** (critical)
   - Basic functionality tests
   - Fast execution
   - No external dependencies

2. **Integration Tests** (critical)
   - Service integration tests
   - Real service communication
   - Database interactions

3. **Health Tests** (critical)
   - Service uptime checks
   - Dependency validation
   - Performance baselines

4. **API Tests**
   - Endpoint functionality
   - Request/response validation
   - Error handling

5. **Performance Tests**
   - Benchmark tests
   - Response time validation
   - Load testing

6. **Property Tests**
   - Property-based testing
   - Edge case discovery
   - Input validation

### **Test Execution Flow**

```mermaid
graph TD
    A[Start Test Runner] --> B[Run Health Checks]
    B --> C{All Critical Services Healthy?}
    C -->|No| D[Report Failures & Exit]
    C -->|Yes| E[Execute Test Suites in Parallel]
    E --> F[Collect Results]
    F --> G[Generate Report]
    G --> H{All Critical Tests Pass?}
    H -->|Yes| I[Success]
    H -->|No| J[Failure]
```

## 📊 **Health Check Features**

### **Service Health Monitoring**

- **Response Time Tracking**: Measures service response times
- **Status Code Validation**: Checks HTTP status codes
- **Retry Logic**: Automatic retries with exponential backoff
- **Timeout Handling**: Configurable timeouts per service

### **Performance Baselines**

```python
performance_thresholds = {
    'api': {'p50': 0.5, 'p95': 2.0, 'p99': 5.0},
    'status': {'p50': 1.0, 'p95': 3.0, 'p99': 8.0},
    'minio': {'p50': 0.3, 'p95': 1.0, 'p99': 3.0},
    'elasticsearch': {'p50': 1.0, 'p95': 5.0, 'p99': 10.0}
}
```

### **Load Testing**

- **Concurrent Requests**: Tests service under load
- **Success Rate Monitoring**: Tracks request success rates
- **Performance Degradation**: Detects performance issues

## 🔗 **Integration with CI/CD**

### **GitHub Actions Integration**

```yaml
- name: Run Tests with Health Checks
  run: |
    python scripts/run-tests-with-health-check.py --workers 4
```

### **Pre-commit Hooks**

```yaml
- repo: local
  hooks:
    - id: health-check
      name: Service Health Check
      entry: python scripts/demo-health-checks.py
      language: system
      pass_filenames: false
```

## 🛠️ **Customization**

### **Adding New Services**

1. **Update service configuration** in `config/service-urls.json`
2. **Add fallback URL** in health check classes
3. **Configure health endpoint** for the service
4. **Add to critical or optional services** list

### **Custom Health Checks**

```python
class CustomServiceChecker(ServiceUptimeChecker):
    async def check_custom_service(self, session):
        # Custom health check logic
        pass
```

### **Performance Thresholds**

Adjust performance thresholds based on your environment:

```python
# In test configuration
performance_thresholds = {
    'api': {'p50': 1.0, 'p95': 3.0, 'p99': 10.0},  # Relaxed for dev
}
```

## 📈 **Monitoring & Reporting**

### **Health Check Reports**

```
📊 Service Health Report:
Environment: development
Timestamp: 2025-06-13 12:56:58
Total services: 8

✅ Healthy Services (5):
  ✅ api: 0.008s
  ✅ status: 0.006s
  ✅ frontend: 0.005s

❌ Unhealthy Services (3):
  💥 docs: error
    └─ All URLs failed or timed out
```

### **Test Execution Summary**

```
📋 TEST EXECUTION SUMMARY
Total test suites: 6
Successful: 5
Failed: 1
Total duration: 45.2s

✅ ALL CRITICAL TESTS PASSED!
```

## 🎯 **Best Practices**

1. **Always run health checks** before executing tests
2. **Use relative URLs** for same-origin requests
3. **Configure appropriate timeouts** for your environment
4. **Monitor performance trends** over time
5. **Update fallback URLs** when ports change
6. **Test in all environments** before deployment

## 🔍 **Troubleshooting**

### **Common Issues**

**DNS Resolution Errors**:
```bash
# Add to /etc/hosts
127.0.0.1 api.turdparty.localhost
127.0.0.1 status.turdparty.localhost
```

**Service Timeouts**:
```python
# Increase timeout in health checker
uptime_checker = ServiceUptimeChecker(timeout=30)
```

**Port Conflicts**:
```bash
# Check port usage
netstat -tulpn | grep :8000
```

### **Debug Mode**

```bash
# Run with verbose output
python -m pytest tests/health/ -v -s

# Run single health check
python -c "
import asyncio
from tests.health.test_service_uptime import uptime_checker
asyncio.run(uptime_checker.check_all_services())
"
```

## 🎉 **Benefits**

- ✅ **Reliable Testing**: Ensures services are healthy before testing
- ✅ **Fast Feedback**: Parallel execution with early failure detection
- ✅ **Environment Agnostic**: Works with Traefik or direct ports
- ✅ **Comprehensive Reporting**: Detailed health and test reports
- ✅ **Performance Monitoring**: Tracks service performance over time
- ✅ **Easy Integration**: Simple CI/CD integration
